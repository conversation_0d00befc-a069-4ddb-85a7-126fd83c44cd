"""
VPN and Proxy Manager for Email Scraper
Provides IP rotation and location changing capabilities for high-volume scraping
"""

import time
import random
import requests
import subprocess
import json
from typing import List, Dict, Optional
from fake_useragent import UserAgent

class VPNManager:
    def __init__(self):
        self.current_proxy = None
        self.proxy_list = []
        self.vpn_providers = []
        self.user_agent = UserAgent()
        self.session = requests.Session()
        
    def load_free_proxies(self):
        """Load free proxy servers from various sources"""
        free_proxy_sources = [
            "https://www.proxy-list.download/api/v1/get?type=http",
            "https://api.proxyscrape.com/v2/?request=get&protocol=http&timeout=10000&country=all",
        ]
        
        proxies = []
        
        # Add some reliable free proxy servers (these change frequently)
        static_proxies = [
            {"ip": "*******", "port": "8080", "country": "US"},
            {"ip": "*******", "port": "8080", "country": "US"},
            {"ip": "***************", "port": "8080", "country": "US"},
            {"ip": "***************", "port": "8080", "country": "US"},
            {"ip": "***************", "port": "8080", "country": "US"},
            {"ip": "***************", "port": "8080", "country": "US"},
        ]
        
        proxies.extend(static_proxies)
        
        # Try to fetch from online sources
        for source in free_proxy_sources:
            try:
                response = requests.get(source, timeout=10)
                if response.status_code == 200:
                    # Parse proxy data (format varies by source)
                    proxy_data = response.text.strip().split('\n')
                    for proxy in proxy_data[:10]:  # Limit to first 10
                        if ':' in proxy:
                            ip, port = proxy.split(':')
                            proxies.append({
                                "ip": ip.strip(),
                                "port": port.strip(),
                                "country": "Unknown"
                            })
            except Exception as e:
                print(f"Failed to fetch proxies from {source}: {e}")
                continue
        
        self.proxy_list = proxies
        print(f"📡 Loaded {len(self.proxy_list)} proxy servers")
        return proxies
    
    def test_proxy(self, proxy_dict, timeout=10):
        """Test if a proxy is working"""
        proxy_url = f"http://{proxy_dict['ip']}:{proxy_dict['port']}"
        proxies = {
            'http': proxy_url,
            'https': proxy_url
        }
        
        try:
            response = requests.get(
                'http://httpbin.org/ip', 
                proxies=proxies, 
                timeout=timeout,
                headers={'User-Agent': self.user_agent.random}
            )
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Proxy {proxy_dict['ip']}:{proxy_dict['port']} working - IP: {result.get('origin', 'Unknown')}")
                return True
        except Exception as e:
            print(f"❌ Proxy {proxy_dict['ip']}:{proxy_dict['port']} failed: {e}")
            return False
        
        return False
    
    def get_working_proxies(self, max_test=20):
        """Get list of working proxies"""
        if not self.proxy_list:
            self.load_free_proxies()
        
        working_proxies = []
        test_count = 0
        
        print("🔍 Testing proxy servers...")
        for proxy in self.proxy_list:
            if test_count >= max_test:
                break
                
            if self.test_proxy(proxy):
                working_proxies.append(proxy)
                
            test_count += 1
            time.sleep(1)  # Rate limit testing
        
        print(f"✅ Found {len(working_proxies)} working proxies out of {test_count} tested")
        return working_proxies
    
    def rotate_proxy(self, working_proxies):
        """Rotate to a new proxy"""
        if not working_proxies:
            print("⚠️ No working proxies available")
            return None
            
        new_proxy = random.choice(working_proxies)
        self.current_proxy = new_proxy
        print(f"🔄 Rotated to proxy: {new_proxy['ip']}:{new_proxy['port']} ({new_proxy['country']})")
        return new_proxy
    
    def get_proxy_config(self):
        """Get current proxy configuration for Chrome"""
        if not self.current_proxy:
            return None
            
        return {
            'proxy_server': f"{self.current_proxy['ip']}:{self.current_proxy['port']}",
            'proxy_type': 'http'
        }
    
    def check_current_ip(self):
        """Check current public IP address"""
        try:
            if self.current_proxy:
                proxy_url = f"http://{self.current_proxy['ip']}:{self.current_proxy['port']}"
                proxies = {'http': proxy_url, 'https': proxy_url}
                response = requests.get('http://httpbin.org/ip', proxies=proxies, timeout=10)
            else:
                response = requests.get('http://httpbin.org/ip', timeout=10)
                
            if response.status_code == 200:
                ip_info = response.json()
                current_ip = ip_info.get('origin', 'Unknown')
                print(f"🌐 Current IP: {current_ip}")
                return current_ip
        except Exception as e:
            print(f"❌ Failed to check IP: {e}")
            return None
    
    def get_location_info(self):
        """Get current location information"""
        try:
            if self.current_proxy:
                proxy_url = f"http://{self.current_proxy['ip']}:{self.current_proxy['port']}"
                proxies = {'http': proxy_url, 'https': proxy_url}
                response = requests.get('http://ip-api.com/json/', proxies=proxies, timeout=10)
            else:
                response = requests.get('http://ip-api.com/json/', timeout=10)
                
            if response.status_code == 200:
                location_info = response.json()
                print(f"📍 Location: {location_info.get('city', 'Unknown')}, {location_info.get('country', 'Unknown')}")
                print(f"🏢 ISP: {location_info.get('isp', 'Unknown')}")
                return location_info
        except Exception as e:
            print(f"❌ Failed to get location info: {e}")
            return None

class TorManager:
    """Tor network manager for enhanced anonymity"""
    
    def __init__(self):
        self.tor_process = None
        self.tor_port = 9050
        self.control_port = 9051
        
    def start_tor(self):
        """Start Tor service (requires Tor to be installed)"""
        try:
            # Check if Tor is already running
            response = requests.get(
                'http://httpbin.org/ip',
                proxies={'http': f'socks5://127.0.0.1:{self.tor_port}',
                        'https': f'socks5://127.0.0.1:{self.tor_port}'},
                timeout=10
            )
            print("✅ Tor is already running")
            return True
        except:
            pass
            
        try:
            # Try to start Tor (Windows)
            self.tor_process = subprocess.Popen(['tor'], 
                                              stdout=subprocess.PIPE, 
                                              stderr=subprocess.PIPE)
            time.sleep(10)  # Wait for Tor to start
            print("✅ Tor started successfully")
            return True
        except Exception as e:
            print(f"❌ Failed to start Tor: {e}")
            print("💡 Please install Tor Browser or Tor service manually")
            return False
    
    def get_tor_proxy_config(self):
        """Get Tor proxy configuration"""
        return {
            'proxy_server': f'socks5://127.0.0.1:{self.tor_port}',
            'proxy_type': 'socks5'
        }
    
    def new_tor_identity(self):
        """Request new Tor identity (new IP)"""
        try:
            import socket
            s = socket.socket()
            s.connect(('127.0.0.1', self.control_port))
            s.send(b'AUTHENTICATE\r\n')
            s.recv(1024)
            s.send(b'SIGNAL NEWNYM\r\n')
            s.recv(1024)
            s.close()
            print("🔄 Requested new Tor identity")
            time.sleep(5)  # Wait for new circuit
            return True
        except Exception as e:
            print(f"❌ Failed to get new Tor identity: {e}")
            return False

# VPN Provider configurations (requires manual setup)
VPN_PROVIDERS = {
    "nordvpn": {
        "connect_cmd": "nordvpn connect {country}",
        "disconnect_cmd": "nordvpn disconnect",
        "status_cmd": "nordvpn status",
        "countries": ["us", "uk", "ca", "de", "fr", "jp", "au", "nl", "se", "ch"]
    },
    "expressvpn": {
        "connect_cmd": "expressvpn connect {country}",
        "disconnect_cmd": "expressvpn disconnect",
        "status_cmd": "expressvpn status",
        "countries": ["usa", "uk", "canada", "germany", "france", "japan", "australia"]
    },
    "surfshark": {
        "connect_cmd": "surfshark-vpn attack {country}",
        "disconnect_cmd": "surfshark-vpn down",
        "status_cmd": "surfshark-vpn status",
        "countries": ["us", "uk", "ca", "de", "fr", "jp", "au", "nl", "se"]
    }
}

class CommercialVPNManager:
    """Manager for commercial VPN services"""
    
    def __init__(self, provider="nordvpn"):
        self.provider = provider
        self.config = VPN_PROVIDERS.get(provider, {})
        self.current_country = None
        
    def connect_vpn(self, country=None):
        """Connect to VPN in specified country"""
        if not self.config:
            print(f"❌ VPN provider {self.provider} not configured")
            return False
            
        if not country:
            country = random.choice(self.config.get("countries", ["us"]))
            
        try:
            cmd = self.config["connect_cmd"].format(country=country)
            result = subprocess.run(cmd.split(), capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                self.current_country = country
                print(f"✅ Connected to {self.provider} VPN in {country}")
                return True
            else:
                print(f"❌ Failed to connect to VPN: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ VPN connection error: {e}")
            return False
    
    def disconnect_vpn(self):
        """Disconnect from VPN"""
        try:
            cmd = self.config["disconnect_cmd"]
            result = subprocess.run(cmd.split(), capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print(f"✅ Disconnected from {self.provider} VPN")
                self.current_country = None
                return True
            else:
                print(f"❌ Failed to disconnect from VPN: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ VPN disconnection error: {e}")
            return False
    
    def rotate_vpn_location(self):
        """Rotate to a new VPN location"""
        if not self.config:
            return False
            
        # Disconnect current connection
        self.disconnect_vpn()
        time.sleep(5)
        
        # Connect to new random country
        new_country = random.choice(self.config.get("countries", ["us"]))
        return self.connect_vpn(new_country)
