import time
import random
import re
import json
import csv
import asyncio
import aiohttp
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Set, List
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON>riverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.chrome.options import Options
import undetected_chromedriver as uc
from bs4 import BeautifulSoup
from fake_useragent import UserAgent
import requests
from urllib.parse import quote_plus
import pandas as pd
from datetime import datetime

class AdvancedEmailScraper:
    def __init__(self, headless=False, enable_captcha_detection=True, max_workers=5):
        self.contacts = []  # Changed from emails set to contacts list for comprehensive data
        self.emails = set()  # Keep for backward compatibility
        self.visited_urls = set()
        self.driver = None
        self.headless = headless
        self.enable_captcha_detection = enable_captcha_detection
        self.max_workers = max_workers

        # Enhanced regex patterns for comprehensive data extraction
        self.email_pattern = re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')
        self.phone_pattern = re.compile(r'(\+?1?[-.\s]?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}|\+?[0-9]{1,4}[-.\s]?[0-9]{3,4}[-.\s]?[0-9]{3,4}[-.\s]?[0-9]{3,4})')
        self.linkedin_pattern = re.compile(r'linkedin\.com/in/([a-zA-Z0-9-]+)')
        self.company_pattern = re.compile(r'(?:at|@|works?\s+(?:at|for)|employed\s+(?:at|by))\s+([A-Z][a-zA-Z\s&.,]+(?:Inc|LLC|Corp|Ltd|Co|Company|Technologies|Tech|Solutions|Systems|Group|Associates)?)', re.IGNORECASE)

        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
    def setup_driver(self):
        """Setup undetected Chrome driver with enhanced anti-detection measures"""
        options = uc.ChromeOptions()

        # Enhanced anti-detection options
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-plugins-discovery')
        options.add_argument('--disable-web-security')
        options.add_argument('--disable-features=VizDisplayCompositor')
        options.add_argument('--disable-ipc-flooding-protection')

        # Additional stealth options (commented out problematic ones)
        # options.add_experimental_option("excludeSwitches", ["enable-automation", "enable-logging"])
        # options.add_experimental_option('useAutomationExtension', False)

        # Disable images and CSS for faster loading and less detection
        prefs = {
            "profile.managed_default_content_settings.images": 2,
            "profile.default_content_setting_values.notifications": 2,
            "profile.managed_default_content_settings.stylesheets": 2,
            "profile.managed_default_content_settings.cookies": 1,
            "profile.managed_default_content_settings.javascript": 1,
            "profile.managed_default_content_settings.plugins": 1,
            "profile.managed_default_content_settings.popups": 2,
            "profile.managed_default_content_settings.geolocation": 2,
            "profile.managed_default_content_settings.media_stream": 2,
        }
        options.add_experimental_option("prefs", prefs)

        if self.headless:
            options.add_argument('--headless')

        # Use a more realistic user agent
        ua = UserAgent()
        options.add_argument(f'--user-agent={ua.random}')

        try:
            self.driver = uc.Chrome(options=options, version_main=None)
        except Exception as e:
            print(f"Failed to create undetected Chrome driver: {e}")
            print("Falling back to regular Chrome driver...")
            self.driver = uc.Chrome(options=options)

        # Enhanced anti-detection scripts
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        self.driver.execute_script("Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]})")
        self.driver.execute_script("Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en']})")
        self.driver.execute_script("window.chrome = { runtime: {} }")

        # Set realistic window size
        self.driver.set_window_size(1366, 768)

    def detect_and_handle_captcha(self, skip_detection=False):
        """Detect and handle CAPTCHA challenges with improved detection"""
        if skip_detection:
            return True

        # More specific CAPTCHA indicators to reduce false positives
        captcha_indicators = [
            "i'm not a robot",
            "recaptcha",
            "verify you are human",
            "unusual traffic from your computer network",
            "automated queries",
            "security check required"
        ]

        page_source = self.driver.page_source.lower()

        # Check for specific CAPTCHA elements in the DOM
        captcha_elements = [
            "//iframe[contains(@src, 'recaptcha')]",
            "//div[@class='g-recaptcha']",
            "//*[contains(@class, 'captcha')]",
            "//*[contains(text(), \"I'm not a robot\")]"
        ]

        # First check for actual CAPTCHA elements
        captcha_found = False
        for xpath in captcha_elements:
            try:
                element = self.driver.find_element(By.XPATH, xpath)
                if element.is_displayed():
                    captcha_found = True
                    break
            except:
                continue

        # Then check page source for indicators (more restrictive)
        if not captcha_found:
            for indicator in captcha_indicators:
                if indicator in page_source and len(page_source) < 10000:  # Only check on smaller pages
                    captcha_found = True
                    break

        if captcha_found:
            print(f"🤖 CAPTCHA detected on page")
            print("⏳ Waiting for manual CAPTCHA resolution...")
            print("Please solve the CAPTCHA manually in the browser window.")
            print("💡 Tip: If no CAPTCHA is visible, press Ctrl+C to skip detection")

            # Wait for user to solve CAPTCHA (reduced to 1 minute)
            wait_time = 0
            max_wait = 60  # 1 minute

            while wait_time < max_wait:
                time.sleep(3)
                wait_time += 3

                # Check if CAPTCHA elements are still present
                captcha_still_present = False
                for xpath in captcha_elements:
                    try:
                        element = self.driver.find_element(By.XPATH, xpath)
                        if element.is_displayed():
                            captcha_still_present = True
                            break
                    except:
                        continue

                if not captcha_still_present:
                    print("✅ CAPTCHA appears to be resolved!")
                    return True

                if wait_time % 15 == 0:  # Print status every 15 seconds
                    print(f"⏳ Still waiting... ({wait_time}/{max_wait} seconds)")

            print("⚠️ CAPTCHA resolution timeout. Continuing anyway...")
            return False

        return True  # No CAPTCHA detected

    def smart_delay(self, min_delay=0.5, max_delay=1.5):
        """Add minimal delays for speed"""
        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)

    def fast_http_scrape(self, url, timeout=5):
        """Fast HTTP-based scraping without browser"""
        try:
            response = self.session.get(url, timeout=timeout)
            if response.status_code == 200:
                return self.extract_emails_from_text(response.text)
        except:
            pass
        return set()

    def extract_emails_from_text(self, text):
        """Extract emails from text content"""
        emails = set()
        matches = self.email_pattern.findall(text)
        for email in matches:
            if self.is_valid_email(email):
                emails.add(email.lower())
        return emails

    def is_valid_email(self, email):
        """Validate email format and filter out common false positives"""
        if not email or len(email) < 5:
            return False

        # Filter out common false positives
        invalid_patterns = [
            'example.com', 'test.com', 'domain.com', 'email.com',
            'yourname@', 'name@', 'user@', 'admin@localhost',
            '.png', '.jpg', '.gif', '.css', '.js', 'noreply@',
            'no-reply@', 'donotreply@', 'support@example'
        ]

        email_lower = email.lower()
        for pattern in invalid_patterns:
            if pattern in email_lower:
                return False

        return True

    def extract_phone_numbers(self, text):
        """Extract phone numbers from text"""
        phones = set()
        matches = self.phone_pattern.findall(text)
        for phone in matches:
            # Clean and validate phone number
            cleaned_phone = re.sub(r'[^\d+]', '', phone)
            if len(cleaned_phone) >= 10:  # Minimum valid phone length
                phones.add(phone.strip())
        return phones

    def extract_companies(self, text):
        """Extract company names from text"""
        companies = set()
        matches = self.company_pattern.findall(text)
        for company in matches:
            company = company.strip()
            if len(company) > 2 and company not in ['the', 'and', 'or', 'but', 'for', 'at']:
                companies.add(company)
        return companies

    def extract_linkedin_profiles(self, text):
        """Extract LinkedIn profile URLs"""
        profiles = set()
        matches = self.linkedin_pattern.findall(text)
        for profile in matches:
            profiles.add(f"linkedin.com/in/{profile}")
        return profiles

    def extract_locations(self, text):
        """Extract city and location information"""
        # Common location patterns
        location_patterns = [
            r'(?:in|from|based\s+in|located\s+in)\s+([A-Z][a-zA-Z\s]+(?:,\s*[A-Z]{2,})?)',
            r'([A-Z][a-zA-Z\s]+,\s*[A-Z]{2,3})',  # City, State/Country
            r'([A-Z][a-zA-Z\s]+,\s*[A-Z][a-zA-Z\s]+)'  # City, Country
        ]

        locations = set()
        for pattern in location_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for location in matches:
                location = location.strip()
                if len(location) > 3:
                    locations.add(location)
        return locations

    def human_like_scroll(self, pause_time=None):
        """Simulate human-like scrolling behavior"""
        if pause_time is None:
            pause_time = random.uniform(0.5, 2.0)
            
        # Get page height
        last_height = self.driver.execute_script("return document.body.scrollHeight")
        
        while True:
            # Random scroll amount
            scroll_amount = random.randint(200, 800)
            self.driver.execute_script(f"window.scrollBy(0, {scroll_amount});")
            
            # Random pause
            time.sleep(random.uniform(0.1, 0.5))
            
            # Check if reached bottom
            new_height = self.driver.execute_script("return document.body.scrollHeight")
            current_position = self.driver.execute_script("return window.pageYOffset + window.innerHeight")
            
            if current_position >= new_height:
                break
                
        time.sleep(pause_time)
        
    def extract_comprehensive_data_from_page(self, target_profession="", target_city="", target_country=""):
        """Extract comprehensive professional data from current page"""
        page_source = self.driver.page_source

        # Extract all data types
        emails = self.extract_emails_from_text(page_source)
        phones = self.extract_phone_numbers(page_source)
        companies = self.extract_companies(page_source)
        linkedin_profiles = self.extract_linkedin_profiles(page_source)
        locations = self.extract_locations(page_source)

        # Create comprehensive contact records
        contacts_found = []

        # If we have emails, create detailed records
        for email in emails:
            contact = {
                'profession_name': target_profession,
                'email': email,
                'phone_number': '',
                'company_name': '',
                'city_name': target_city,
                'country_name': target_country,
                'linkedin_profile': '',
                'domain': email.split('@')[1] if '@' in email else '',
                'username': email.split('@')[0] if '@' in email else '',
                'full_name': '',
                'job_title': '',
                'industry': '',
                'experience_level': '',
                'skills': '',
                'website': '',
                'scraped_date': datetime.now().strftime("%Y-%m-%d"),
                'scraped_time': datetime.now().strftime("%H:%M:%S"),
                'source_url': self.driver.current_url if self.driver else '',
                'data_quality_score': 1  # Base score for having email
            }

            # Try to match additional data to this email
            email_domain = email.split('@')[1] if '@' in email else ''

            # Match company names (prioritize those matching email domain)
            for company in companies:
                if email_domain.lower() in company.lower() or company.lower() in email_domain.lower():
                    contact['company_name'] = company
                    contact['data_quality_score'] += 2
                    break

            # If no domain match, use first company found
            if not contact['company_name'] and companies:
                contact['company_name'] = list(companies)[0]
                contact['data_quality_score'] += 1

            # Add phone number (first available)
            if phones:
                contact['phone_number'] = list(phones)[0]
                contact['data_quality_score'] += 2

            # Add LinkedIn profile (first available)
            if linkedin_profiles:
                contact['linkedin_profile'] = list(linkedin_profiles)[0]
                contact['data_quality_score'] += 2

            # Add location information
            if locations:
                location = list(locations)[0]
                if ',' in location:
                    parts = location.split(',')
                    contact['city_name'] = parts[0].strip()
                    if len(parts) > 1:
                        contact['country_name'] = parts[1].strip()
                contact['data_quality_score'] += 1

            # Extract additional context around email
            email_context = self.extract_context_around_email(page_source, email)
            if email_context:
                contact.update(email_context)

            contacts_found.append(contact)

        return contacts_found

    def extract_emails_from_page(self) -> Set[str]:
        """Extract emails from current page - optimized version"""
        emails = set()

        # Direct regex search on page source (faster than BeautifulSoup)
        page_source = self.driver.page_source
        found_emails = self.email_pattern.findall(page_source)

        for email in found_emails:
            if self.is_valid_email(email):
                emails.add(email.lower())

        return emails

    def extract_context_around_email(self, text, email):
        """Extract contextual information around an email address"""
        context_data = {}

        # Find the email in text and get surrounding context
        email_index = text.lower().find(email.lower())
        if email_index == -1:
            return context_data

        # Get 500 characters before and after email
        start = max(0, email_index - 500)
        end = min(len(text), email_index + len(email) + 500)
        context = text[start:end]

        # Extract job titles
        job_title_patterns = [
            r'(?:Senior|Lead|Principal|Chief|Head of|Director of|Manager|Specialist|Engineer|Developer|Analyst|Consultant|Expert)\s+[A-Za-z\s]+',
            r'[A-Za-z\s]+(?:Engineer|Developer|Manager|Director|Analyst|Specialist|Consultant|Expert|Lead|Senior)'
        ]

        for pattern in job_title_patterns:
            matches = re.findall(pattern, context, re.IGNORECASE)
            if matches:
                context_data['job_title'] = matches[0].strip()
                break

        # Extract full names (common patterns)
        name_patterns = [
            r'([A-Z][a-z]+\s+[A-Z][a-z]+)',  # First Last
            r'([A-Z][a-z]+\s+[A-Z]\.\s+[A-Z][a-z]+)',  # First M. Last
        ]

        for pattern in name_patterns:
            matches = re.findall(pattern, context)
            if matches:
                context_data['full_name'] = matches[0].strip()
                break

        # Extract experience level indicators
        if any(word in context.lower() for word in ['senior', 'lead', 'principal', 'chief']):
            context_data['experience_level'] = 'Senior'
        elif any(word in context.lower() for word in ['junior', 'entry', 'associate']):
            context_data['experience_level'] = 'Junior'
        else:
            context_data['experience_level'] = 'Mid-level'

        return context_data

    def fast_google_search(self, query, max_results=50):
        """Fast Google search using multiple search engines"""
        emails = set()

        # Multiple search engines for better coverage
        search_engines = [
            f"https://www.google.com/search?q={quote_plus(query)}&num=50",
            f"https://www.bing.com/search?q={quote_plus(query)}&count=50"
        ]

        with ThreadPoolExecutor(max_workers=2) as executor:
            futures = [executor.submit(self.fast_http_scrape, url) for url in search_engines]
            for future in as_completed(futures):
                try:
                    result_emails = future.result()
                    emails.update(result_emails)
                except:
                    continue

        return emails
        
    def scrape_google_search_comprehensive(self, query: str, target_profession="", target_city="", target_country="", max_pages: int = 10):
        """Comprehensive Google search scraping with detailed data extraction"""
        contacts_found = []

        search_url = f"https://www.google.com/search?q={query}&num=100"  # Get more results per page
        self.driver.get(search_url)

        # Quick cookie consent handling
        try:
            accept_button = WebDriverWait(self.driver, 2).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Accept') or contains(text(), 'I agree')]"))
            )
            accept_button.click()
        except:
            pass

        for page in range(max_pages):
            print(f"Scraping Google search page {page + 1}")

            # Extract comprehensive data from current page
            page_contacts = self.extract_comprehensive_data_from_page(target_profession, target_city, target_country)
            contacts_found.extend(page_contacts)

            # Also extract emails for backward compatibility
            page_emails = self.extract_emails_from_page()
            self.emails.update(page_emails)

            print(f"Found {len(page_contacts)} detailed contacts and {len(page_emails)} emails on this page")

            # Minimal delay
            self.smart_delay(0.5, 1.0)

            # Try to go to next page quickly
            try:
                next_button = self.driver.find_element(By.ID, "pnnext")
                if next_button.is_enabled():
                    next_button.click()
                    self.smart_delay(1, 2)  # Minimal delay after clicking
                else:
                    break
            except Exception as e:
                print(f"No more pages or error navigating: {e}")
                break

        return contacts_found

    def scrape_google_search(self, query: str, max_pages: int = 10) -> Set[str]:
        """Fast Google search scraping with minimal delays - backward compatibility"""
        emails = set()

        search_url = f"https://www.google.com/search?q={query}&num=100"  # Get more results per page
        self.driver.get(search_url)

        # Quick cookie consent handling
        try:
            accept_button = WebDriverWait(self.driver, 2).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Accept') or contains(text(), 'I agree')]"))
            )
            accept_button.click()
        except:
            pass

        for page in range(max_pages):
            print(f"Scraping Google search page {page + 1}")

            # Extract emails from current page
            page_emails = self.extract_emails_from_page()
            emails.update(page_emails)
            print(f"Found {len(page_emails)} emails on this page")

            # Minimal delay
            self.smart_delay(0.5, 1.0)

            # Try to go to next page quickly
            try:
                next_button = self.driver.find_element(By.ID, "pnnext")
                if next_button.is_enabled():
                    next_button.click()
                    self.smart_delay(1, 2)  # Minimal delay after clicking
                else:
                    break
            except Exception as e:
                print(f"No more pages or error navigating: {e}")
                break

        return emails
        
    def scrape_website(self, url: str) -> Set[str]:
        """Scrape emails from a specific website with CAPTCHA handling"""
        emails = set()

        try:
            print(f"Scraping: {url}")
            self.driver.get(url)

            # Check for CAPTCHA after loading the website (if enabled)
            if self.enable_captcha_detection and not self.detect_and_handle_captcha():
                print(f"⚠️ CAPTCHA detected on {url}, but continuing...")

            self.smart_delay(3, 6)

            # Scroll and extract emails
            self.human_like_scroll()
            page_emails = self.extract_emails_from_page()
            emails.update(page_emails)
            print(f"Found {len(page_emails)} emails on {url}")

        except Exception as e:
            print(f"Error scraping {url}: {e}")

        return emails

    def parallel_fast_scrape(self, queries: List[str]) -> Set[str]:
        """Parallel scraping using HTTP requests for maximum speed"""
        all_emails = set()

        print(f"🚀 Starting parallel fast scraping for {len(queries)} queries...")

        # Create massively expanded query list for maximum coverage
        expanded_queries = []
        for query in queries:
            expanded_queries.extend([
                f"{query} email contact",
                f"{query} directory",
                f"{query} contact information",
                f"{query} professional email",
                f"{query} business contact",
                f"{query} email address",
                f"{query} contact details",
                f"{query} email list",
                f"{query} contact directory",
                f"{query} professional contact"
            ])

        # Use ThreadPoolExecutor for parallel HTTP requests
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = []

            for query in expanded_queries:
                # Submit multiple search engines per query for maximum coverage
                search_urls = [
                    f"https://www.google.com/search?q={quote_plus(query)}&num=100",
                    f"https://www.bing.com/search?q={quote_plus(query)}&count=50",
                    f"https://duckduckgo.com/?q={quote_plus(query)}",
                    f"https://search.yahoo.com/search?p={quote_plus(query)}"
                ]

                for url in search_urls:
                    futures.append(executor.submit(self.fast_http_scrape, url))

            # Collect results as they complete
            completed = 0
            for future in as_completed(futures):
                try:
                    result_emails = future.result()
                    all_emails.update(result_emails)
                    completed += 1

                    if completed % 10 == 0:
                        print(f"⚡ Processed {completed}/{len(futures)} requests, found {len(all_emails)} emails so far")

                except Exception:
                    completed += 1
                    continue

        print(f"✅ Parallel scraping completed! Found {len(all_emails)} unique emails")
        return all_emails

    def scrape_multiple_sources_comprehensive(self, sources: List[str], target_profession="", target_city="", target_country=""):
        """Comprehensive scraping from multiple sources with detailed data extraction"""
        all_contacts = []
        all_emails = set()

        # Separate URLs from search queries
        urls = [source for source in sources if source.startswith("http")]
        queries = [source for source in sources if not source.startswith("http")]

        # Use parallel fast scraping for search queries
        if queries:
            print("🚀 Using parallel fast scraping for search queries...")
            fast_emails = self.parallel_fast_scrape(queries)
            all_emails.update(fast_emails)

            # Convert fast emails to contact format
            for email in fast_emails:
                contact = {
                    'profession_name': target_profession,
                    'email': email,
                    'phone_number': '',
                    'company_name': email.split('@')[1] if '@' in email else '',
                    'city_name': target_city,
                    'country_name': target_country,
                    'linkedin_profile': '',
                    'domain': email.split('@')[1] if '@' in email else '',
                    'username': email.split('@')[0] if '@' in email else '',
                    'full_name': '',
                    'job_title': '',
                    'industry': '',
                    'experience_level': '',
                    'skills': '',
                    'website': '',
                    'scraped_date': datetime.now().strftime("%Y-%m-%d"),
                    'scraped_time': datetime.now().strftime("%H:%M:%S"),
                    'source_url': 'parallel_search',
                    'data_quality_score': 1
                }
                all_contacts.append(contact)

        # Use browser scraping for comprehensive data extraction
        if len(all_emails) < 100 and queries:
            print("📈 Boosting results with comprehensive browser scraping...")
            for query in queries[:3]:  # First 3 queries for detailed extraction
                try:
                    contacts = self.scrape_google_search_comprehensive(
                        query, target_profession, target_city, target_country, max_pages=3
                    )
                    all_contacts.extend(contacts)
                    print(f"Comprehensive scraping found {len(contacts)} detailed contacts from {query}")
                except Exception as e:
                    print(f"Error with comprehensive scraping {query}: {e}")
                    continue

        # Use browser scraping for direct URLs (if any)
        if urls:
            print("🌐 Scraping direct URLs with browser...")
            for url in urls:
                try:
                    emails = self.scrape_website(url)
                    all_emails.update(emails)
                    print(f"Total found from {url}: {len(emails)} emails")
                    self.smart_delay(1, 2)  # Minimal delay
                except Exception as e:
                    print(f"Error with URL {url}: {e}")
                    continue

        # Store contacts and emails
        self.contacts.extend(all_contacts)
        self.emails.update(all_emails)

        return all_contacts

    def scrape_multiple_sources(self, sources: List[str]) -> Set[str]:
        """Fast scraping from multiple sources with hybrid approach - backward compatibility"""
        all_emails = set()

        # Separate URLs from search queries
        urls = [source for source in sources if source.startswith("http")]
        queries = [source for source in sources if not source.startswith("http")]

        # Use parallel fast scraping for search queries
        if queries:
            print("🚀 Using parallel fast scraping for search queries...")
            fast_emails = self.parallel_fast_scrape(queries)
            all_emails.update(fast_emails)

        # Use browser scraping for direct URLs (if any)
        if urls:
            print("🌐 Scraping direct URLs with browser...")
            for url in urls:
                try:
                    emails = self.scrape_website(url)
                    all_emails.update(emails)
                    print(f"Total found from {url}: {len(emails)} emails")
                    self.smart_delay(1, 2)  # Minimal delay
                except Exception as e:
                    print(f"Error with URL {url}: {e}")
                    continue

        # If we have few emails, fall back to browser scraping for some queries
        if len(all_emails) < 50 and queries:
            print("📈 Boosting results with browser scraping...")
            for query in queries[:2]:  # Only first 2 queries to save time
                try:
                    emails = self.scrape_google_search(query, max_pages=3)
                    all_emails.update(emails)
                    print(f"Browser scraping found {len(emails)} additional emails from {query}")
                except Exception as e:
                    print(f"Error with browser scraping {query}: {e}")
                    continue

        return all_emails
        
    def save_emails_multiple_formats(self, base_filename="scraped_contacts", formats=None):
        """Save scraped contacts and emails in multiple formats with comprehensive data"""
        if formats is None:
            formats = ['json']  # Default format

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Prepare comprehensive contact data
        contact_data = []

        # Use comprehensive contacts if available, otherwise fall back to emails
        if self.contacts:
            # Remove duplicates based on email
            seen_emails = set()
            unique_contacts = []
            for contact in self.contacts:
                if contact['email'] not in seen_emails:
                    seen_emails.add(contact['email'])
                    unique_contacts.append(contact)

            for i, contact in enumerate(unique_contacts, 1):
                contact_record = {
                    'id': i,
                    'profession_name': contact.get('profession_name', ''),
                    'email': contact.get('email', ''),
                    'phone_number': contact.get('phone_number', ''),
                    'company_name': contact.get('company_name', ''),
                    'city_name': contact.get('city_name', ''),
                    'country_name': contact.get('country_name', ''),
                    'linkedin_profile': contact.get('linkedin_profile', ''),
                    'domain': contact.get('domain', ''),
                    'username': contact.get('username', ''),
                    'full_name': contact.get('full_name', ''),
                    'job_title': contact.get('job_title', ''),
                    'industry': contact.get('industry', ''),
                    'experience_level': contact.get('experience_level', ''),
                    'skills': contact.get('skills', ''),
                    'website': contact.get('website', ''),
                    'scraped_date': contact.get('scraped_date', datetime.now().strftime("%Y-%m-%d")),
                    'scraped_time': contact.get('scraped_time', datetime.now().strftime("%H:%M:%S")),
                    'source_url': contact.get('source_url', ''),
                    'data_quality_score': contact.get('data_quality_score', 1)
                }
                contact_data.append(contact_record)
        else:
            # Fallback to email-only data
            email_list = list(self.emails)
            for i, email in enumerate(email_list, 1):
                contact_record = {
                    'id': i,
                    'profession_name': '',
                    'email': email,
                    'phone_number': '',
                    'company_name': email.split('@')[1] if '@' in email else '',
                    'city_name': '',
                    'country_name': '',
                    'linkedin_profile': '',
                    'domain': email.split('@')[1] if '@' in email else '',
                    'username': email.split('@')[0] if '@' in email else '',
                    'full_name': '',
                    'job_title': '',
                    'industry': '',
                    'experience_level': '',
                    'skills': '',
                    'website': '',
                    'scraped_date': datetime.now().strftime("%Y-%m-%d"),
                    'scraped_time': datetime.now().strftime("%H:%M:%S"),
                    'source_url': '',
                    'data_quality_score': 1
                }
                contact_data.append(contact_record)

        saved_files = []

        # Save in JSON format
        if 'json' in formats:
            json_filename = f"{base_filename}_{timestamp}.json"
            json_data = {
                "metadata": {
                    "total_count": len(contact_data),
                    "total_emails": len(self.emails),
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "scraping_method": "Advanced Email Scraper - Comprehensive Data"
                },
                "contacts": contact_data
            }

            with open(json_filename, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, indent=2, ensure_ascii=False)
            saved_files.append(json_filename)
            print(f"✅ Saved {len(contact_data)} contacts to {json_filename}")

        # Save in CSV format
        if 'csv' in formats:
            csv_filename = f"{base_filename}_{timestamp}.csv"
            df = pd.DataFrame(contact_data)
            df.to_csv(csv_filename, index=False, encoding='utf-8')
            saved_files.append(csv_filename)
            print(f"✅ Saved {len(contact_data)} contacts to {csv_filename}")

        # Save in Excel format
        if 'excel' in formats or 'xlsx' in formats:
            excel_filename = f"{base_filename}_{timestamp}.xlsx"
            df = pd.DataFrame(contact_data)

            with pd.ExcelWriter(excel_filename, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='Emails', index=False)

                # Add summary sheet
                summary_data = {
                    'Metric': ['Total Emails', 'Unique Domains', 'Scraping Date', 'Scraping Time'],
                    'Value': [
                        len(self.emails),
                        len(set(email.split('@')[1] for email in self.emails if '@' in email)),
                        datetime.now().strftime("%Y-%m-%d"),
                        datetime.now().strftime("%H:%M:%S")
                    ]
                }
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='Summary', index=False)

            saved_files.append(excel_filename)
            print(f"✅ Saved {len(self.emails)} emails to {excel_filename}")

        # Save in TXT format (simple list)
        if 'txt' in formats:
            txt_filename = f"{base_filename}_{timestamp}.txt"
            with open(txt_filename, 'w', encoding='utf-8') as f:
                f.write(f"# Scraped Professional Contacts - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"# Total Count: {len(contact_data)}\n\n")
                for contact in contact_data:
                    f.write(f"Email: {contact['email']}\n")
                    if contact['full_name']:
                        f.write(f"Name: {contact['full_name']}\n")
                    if contact['job_title']:
                        f.write(f"Title: {contact['job_title']}\n")
                    if contact['company_name']:
                        f.write(f"Company: {contact['company_name']}\n")
                    if contact['phone_number']:
                        f.write(f"Phone: {contact['phone_number']}\n")
                    f.write("-" * 40 + "\n")
            saved_files.append(txt_filename)
            print(f"✅ Saved {len(contact_data)} contacts to {txt_filename}")

        # Save in XML format
        if 'xml' in formats:
            xml_filename = f"{base_filename}_{timestamp}.xml"
            with open(xml_filename, 'w', encoding='utf-8') as f:
                f.write('<?xml version="1.0" encoding="UTF-8"?>\n')
                f.write('<contacts>\n')
                f.write(f'  <metadata>\n')
                f.write(f'    <total_count>{len(contact_data)}</total_count>\n')
                f.write(f'    <timestamp>{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</timestamp>\n')
                f.write(f'  </metadata>\n')
                f.write(f'  <contact_list>\n')
                for contact in contact_data:
                    f.write(f'    <contact id="{contact["id"]}">\n')
                    f.write(f'      <profession_name>{contact["profession_name"]}</profession_name>\n')
                    f.write(f'      <email>{contact["email"]}</email>\n')
                    f.write(f'      <phone_number>{contact["phone_number"]}</phone_number>\n')
                    f.write(f'      <company_name>{contact["company_name"]}</company_name>\n')
                    f.write(f'      <city_name>{contact["city_name"]}</city_name>\n')
                    f.write(f'      <country_name>{contact["country_name"]}</country_name>\n')
                    f.write(f'      <full_name>{contact["full_name"]}</full_name>\n')
                    f.write(f'      <job_title>{contact["job_title"]}</job_title>\n')
                    f.write(f'    </contact>\n')
                f.write(f'  </contact_list>\n')
                f.write('</contacts>\n')
            saved_files.append(xml_filename)
            print(f"✅ Saved {len(contact_data)} contacts to {xml_filename}")

        # Save in HTML format (table view)
        if 'html' in formats:
            html_filename = f"{base_filename}_{timestamp}.html"
            with open(html_filename, 'w', encoding='utf-8') as f:
                f.write('<!DOCTYPE html>\n<html>\n<head>\n')
                f.write('<title>Professional Contacts Database</title>\n')
                f.write('<style>\n')
                f.write('table { border-collapse: collapse; width: 100%; font-family: Arial, sans-serif; }\n')
                f.write('th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n')
                f.write('th { background-color: #f2f2f2; font-weight: bold; }\n')
                f.write('tr:nth-child(even) { background-color: #f9f9f9; }\n')
                f.write('</style>\n</head>\n<body>\n')
                f.write(f'<h1>Professional Contacts Database</h1>\n')
                f.write(f'<p>Generated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>\n')
                f.write(f'<p>Total Contacts: {len(contact_data)}</p>\n')
                f.write('<table>\n')
                f.write('<tr><th>ID</th><th>Profession</th><th>Email</th><th>Phone</th><th>Company</th><th>Name</th><th>Title</th><th>City</th><th>Country</th></tr>\n')
                for contact in contact_data:
                    f.write(f'<tr>')
                    f.write(f'<td>{contact["id"]}</td>')
                    f.write(f'<td>{contact["profession_name"]}</td>')
                    f.write(f'<td>{contact["email"]}</td>')
                    f.write(f'<td>{contact["phone_number"]}</td>')
                    f.write(f'<td>{contact["company_name"]}</td>')
                    f.write(f'<td>{contact["full_name"]}</td>')
                    f.write(f'<td>{contact["job_title"]}</td>')
                    f.write(f'<td>{contact["city_name"]}</td>')
                    f.write(f'<td>{contact["country_name"]}</td>')
                    f.write(f'</tr>\n')
                f.write('</table>\n</body>\n</html>')
            saved_files.append(html_filename)
            print(f"✅ Saved {len(contact_data)} contacts to {html_filename}")

        # Generate Google Sheets import instructions
        if 'google-sheets' in formats:
            gs_filename = f"{base_filename}_{timestamp}_google_sheets_import.csv"
            df = pd.DataFrame(contact_data)
            df.to_csv(gs_filename, index=False, encoding='utf-8')
            saved_files.append(gs_filename)

            # Create instructions file
            instructions_filename = f"{base_filename}_{timestamp}_google_sheets_instructions.txt"
            with open(instructions_filename, 'w', encoding='utf-8') as f:
                f.write("📊 GOOGLE SHEETS IMPORT INSTRUCTIONS\n")
                f.write("=" * 50 + "\n\n")
                f.write("COMPREHENSIVE PROFESSIONAL CONTACTS DATABASE\n")
                f.write("This file contains detailed professional information including:\n")
                f.write("• Profession Name • Email • Phone Number • Company Name\n")
                f.write("• City • Country • Full Name • Job Title • LinkedIn Profile\n")
                f.write("• Industry • Experience Level • Skills • Data Quality Score\n\n")
                f.write("IMPORT INSTRUCTIONS:\n")
                f.write("1. Open Google Sheets (sheets.google.com)\n")
                f.write("2. Create a new spreadsheet\n")
                f.write("3. Go to File > Import\n")
                f.write("4. Choose 'Upload' tab\n")
                f.write(f"5. Upload the file: {gs_filename}\n")
                f.write("6. Select 'Replace spreadsheet' and click 'Import data'\n\n")
                f.write("Alternative method:\n")
                f.write("1. Open the CSV file in Excel\n")
                f.write("2. Copy all data (Ctrl+A, then Ctrl+C)\n")
                f.write("3. Paste into Google Sheets (Ctrl+V)\n\n")
                f.write(f"Total contacts to import: {len(contact_data)}\n")
                f.write(f"Unique emails: {len(set(c['email'] for c in contact_data))}\n")

            saved_files.append(instructions_filename)
            print(f"✅ Saved {len(contact_data)} contacts for Google Sheets import: {gs_filename}")
            print(f"📋 Import instructions saved: {instructions_filename}")

        print(f"\n📁 Total files saved: {len(saved_files)}")
        return saved_files
        
    def run_scraper_comprehensive(self, sources: List[str], target_profession="", target_city="", target_country="", save_formats=['json']):
        """Main comprehensive scraper execution with detailed data extraction"""
        try:
            self.setup_driver()
            print("Starting comprehensive professional data scraping...")

            # Use comprehensive scraping method
            scraped_contacts = self.scrape_multiple_sources_comprehensive(
                sources, target_profession, target_city, target_country
            )

            print(f"Total contacts found: {len(self.contacts)}")
            print(f"Total emails found: {len(self.emails)}")

            # Save in multiple formats
            if self.contacts or self.emails:
                self.save_emails_multiple_formats(formats=save_formats)
                print(f"📁 Professional data saved in {len(save_formats)} format(s): {', '.join(save_formats)}")
            else:
                print("⚠️ No contacts or emails found to save")

        finally:
            if self.driver:
                self.driver.quit()

    def run_scraper(self, sources: List[str], save_formats=['json']):
        """Main scraper execution with configurable save formats - backward compatibility"""
        try:
            self.setup_driver()
            print("Starting email scraping...")

            scraped_emails = self.scrape_multiple_sources(sources)
            self.emails.update(scraped_emails)

            print(f"Total emails found: {len(self.emails)}")

            # Save in multiple formats
            if self.emails:
                self.save_emails_multiple_formats(formats=save_formats)
                print(f"📁 Emails saved in {len(save_formats)} format(s): {', '.join(save_formats)}")
            else:
                print("⚠️ No emails found to save")

        finally:
            if self.driver:
                self.driver.quit()