import time
import random
import re
import json
import csv
import os
import asyncio
import aiohttp
import subprocess
import socket
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Set, List
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.proxy import Proxy, ProxyType
import undetected_chromedriver as uc
from bs4 import BeautifulSoup
from fake_useragent import UserAgent
import requests
from urllib.parse import quote_plus
import pandas as pd
from datetime import datetime
from vpn_manager import VPNManager, TorManager, CommercialVPNManager

class AdvancedEmailScraper:
    def __init__(self, headless=False, enable_captcha_detection=True, max_workers=5, output_folder="ScrapedEmails",
                 use_vpn=False, vpn_type="proxy", vpn_provider="nordvpn", rotation_interval=50, ethical_mode=True):
        self.contacts = []  # Changed from emails set to contacts list for comprehensive data
        self.emails = set()  # Keep for backward compatibility
        self.visited_urls = set()
        self.driver = None
        self.headless = headless
        self.enable_captcha_detection = enable_captcha_detection
        self.max_workers = max_workers
        self.output_folder = output_folder

        # Ethical scraping configuration
        self.ethical_mode = ethical_mode
        self.robots_txt_cache = {}
        self.rate_limits = {}
        self.last_request_time = {}
        self.blocked_domains = set()
        self.respect_robots_txt = True
        self.max_requests_per_domain = 100  # Limit requests per domain
        self.domain_request_count = {}
        self.ethical_delay_range = (2, 5)  # Longer delays for ethical scraping

        # VPN and IP rotation settings
        self.use_vpn = use_vpn
        self.vpn_type = vpn_type  # "proxy", "tor", or "commercial"
        self.vpn_provider = vpn_provider
        self.rotation_interval = rotation_interval  # Rotate IP every N requests
        self.request_count = 0
        self.working_proxies = []

        # Initialize VPN managers
        self.vpn_manager = None
        self.tor_manager = None
        self.commercial_vpn = None

        if self.use_vpn:
            self.setup_vpn_managers()

        # Create output directory if it doesn't exist
        self.create_output_directory()

        # Enhanced regex patterns for comprehensive data extraction
        self.email_pattern = re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')
        self.phone_pattern = re.compile(r'(\+?1?[-.\s]?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}|\+?[0-9]{1,4}[-.\s]?[0-9]{3,4}[-.\s]?[0-9]{3,4}[-.\s]?[0-9]{3,4})')
        self.linkedin_pattern = re.compile(r'linkedin\.com/in/([a-zA-Z0-9-]+)')
        self.company_pattern = re.compile(r'(?:at|@|works?\s+(?:at|for)|employed\s+(?:at|by))\s+([A-Z][a-zA-Z\s&.,]+(?:Inc|LLC|Corp|Ltd|Co|Company|Technologies|Tech|Solutions|Systems|Group|Associates)?)', re.IGNORECASE)

        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })

        # Initialize ethical scraping
        if self.ethical_mode:
            self.setup_ethical_scraping()

    def setup_ethical_scraping(self):
        """Initialize ethical scraping configurations"""
        print("🌱 Initializing ethical scraping mode...")
        print("✅ Respecting robots.txt files")
        print("✅ Implementing rate limiting")
        print("✅ Using polite delays between requests")
        print("✅ Limiting requests per domain")

        # Set conservative defaults for ethical scraping
        if self.max_workers > 10:
            self.max_workers = 10
            print("⚠️ Reduced max_workers to 10 for ethical scraping")

        if self.rotation_interval < 50:
            self.rotation_interval = 50
            print("⚠️ Increased rotation_interval to 50 for ethical scraping")

    def check_robots_txt(self, url):
        """Check robots.txt for scraping permissions"""
        if not self.respect_robots_txt:
            return True

        try:
            from urllib.parse import urlparse, urljoin
            parsed_url = urlparse(url)
            domain = f"{parsed_url.scheme}://{parsed_url.netloc}"

            if domain in self.robots_txt_cache:
                return self.robots_txt_cache[domain]

            robots_url = urljoin(domain, '/robots.txt')
            response = requests.get(robots_url, timeout=5)

            if response.status_code == 200:
                robots_content = response.text.lower()

                # Check for disallow rules
                if 'disallow: /' in robots_content and 'user-agent: *' in robots_content:
                    print(f"🚫 Robots.txt disallows scraping for {domain}")
                    self.robots_txt_cache[domain] = False
                    self.blocked_domains.add(domain)
                    return False

                # Check for crawl delay
                if 'crawl-delay:' in robots_content:
                    import re
                    delay_match = re.search(r'crawl-delay:\s*(\d+)', robots_content)
                    if delay_match:
                        delay = int(delay_match.group(1))
                        self.rate_limits[domain] = max(delay, 2)
                        print(f"⏱️ Setting crawl delay of {delay}s for {domain}")

            self.robots_txt_cache[domain] = True
            return True

        except Exception as e:
            print(f"⚠️ Could not check robots.txt for {url}: {e}")
            return True  # Allow scraping if robots.txt check fails

    def respect_rate_limit(self, url):
        """Implement rate limiting per domain"""
        if not self.ethical_mode:
            return

        try:
            from urllib.parse import urlparse
            domain = urlparse(url).netloc

            # Check domain request count
            if domain in self.domain_request_count:
                self.domain_request_count[domain] += 1
                if self.domain_request_count[domain] > self.max_requests_per_domain:
                    print(f"🛑 Reached request limit for {domain} ({self.max_requests_per_domain} requests)")
                    self.blocked_domains.add(domain)
                    return
            else:
                self.domain_request_count[domain] = 1

            # Apply rate limiting
            current_time = time.time()
            if domain in self.last_request_time:
                time_since_last = current_time - self.last_request_time[domain]
                required_delay = self.rate_limits.get(domain, random.uniform(*self.ethical_delay_range))

                if time_since_last < required_delay:
                    sleep_time = required_delay - time_since_last
                    print(f"⏳ Rate limiting: waiting {sleep_time:.1f}s for {domain}")
                    time.sleep(sleep_time)

            self.last_request_time[domain] = time.time()

        except Exception as e:
            print(f"⚠️ Rate limiting error: {e}")

    def is_domain_allowed(self, url):
        """Check if domain is allowed for scraping"""
        try:
            from urllib.parse import urlparse
            domain = urlparse(url).netloc

            if domain in self.blocked_domains:
                return False

            return self.check_robots_txt(url)
        except:
            return True

    def ethical_delay(self, base_delay=None):
        """Apply ethical delays between requests"""
        if not self.ethical_mode:
            return

        if base_delay is None:
            delay = random.uniform(*self.ethical_delay_range)
        else:
            delay = base_delay

        time.sleep(delay)

    def create_output_directory(self):
        """Create the output directory if it doesn't exist"""
        try:
            if not os.path.exists(self.output_folder):
                os.makedirs(self.output_folder)
                print(f"📁 Created output directory: {self.output_folder}")
            else:
                print(f"📁 Using existing output directory: {self.output_folder}")
        except Exception as e:
            print(f"⚠️ Error creating output directory: {e}")
            print("📁 Files will be saved in current directory")

    def setup_vpn_managers(self):
        """Initialize VPN managers based on configuration"""
        print(f"🔒 Setting up VPN with type: {self.vpn_type}")

        if self.vpn_type == "proxy":
            self.vpn_manager = VPNManager()
            print("📡 Loading proxy servers...")
            self.working_proxies = self.vpn_manager.get_working_proxies(max_test=10)
            if self.working_proxies:
                self.vpn_manager.rotate_proxy(self.working_proxies)
                self.vpn_manager.check_current_ip()
            else:
                print("⚠️ No working proxies found, continuing without VPN")
                self.use_vpn = False

        elif self.vpn_type == "tor":
            self.tor_manager = TorManager()
            if self.tor_manager.start_tor():
                print("🧅 Tor network initialized")
            else:
                print("⚠️ Tor setup failed, falling back to proxy mode")
                self.vpn_type = "proxy"
                self.setup_vpn_managers()

        elif self.vpn_type == "commercial":
            self.commercial_vpn = CommercialVPNManager(self.vpn_provider)
            if self.commercial_vpn.connect_vpn():
                print(f"🛡️ Connected to {self.vpn_provider} VPN")
            else:
                print(f"⚠️ {self.vpn_provider} VPN setup failed, falling back to proxy mode")
                self.vpn_type = "proxy"
                self.setup_vpn_managers()

    def rotate_ip_if_needed(self):
        """Rotate IP address if rotation interval is reached"""
        self.request_count += 1

        if self.request_count % self.rotation_interval == 0 and self.use_vpn:
            print(f"🔄 Rotating IP after {self.request_count} requests...")

            if self.vpn_type == "proxy" and self.vpn_manager and self.working_proxies:
                old_proxy = self.vpn_manager.current_proxy
                self.vpn_manager.rotate_proxy(self.working_proxies)

                # Update Chrome driver with new proxy
                if self.driver:
                    self.driver.quit()
                    time.sleep(2)
                    self.setup_driver()

            elif self.vpn_type == "tor" and self.tor_manager:
                self.tor_manager.new_tor_identity()

                # Restart driver for new Tor circuit
                if self.driver:
                    self.driver.quit()
                    time.sleep(5)
                    self.setup_driver()

            elif self.vpn_type == "commercial" and self.commercial_vpn:
                self.commercial_vpn.rotate_vpn_location()
                time.sleep(10)  # Wait for VPN to establish

            # Verify new IP
            if self.vpn_manager:
                self.vpn_manager.check_current_ip()

            print("✅ IP rotation completed")

    def get_current_proxy_config(self):
        """Get current proxy configuration for Chrome driver"""
        if not self.use_vpn:
            return None

        if self.vpn_type == "proxy" and self.vpn_manager:
            return self.vpn_manager.get_proxy_config()
        elif self.vpn_type == "tor" and self.tor_manager:
            return self.tor_manager.get_tor_proxy_config()
        else:
            return None
        
    def setup_driver(self):
        """Setup undetected Chrome driver with enhanced anti-detection measures and VPN support"""
        options = uc.ChromeOptions()

        # Enhanced anti-detection options
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-plugins-discovery')
        options.add_argument('--disable-web-security')
        options.add_argument('--disable-features=VizDisplayCompositor')
        options.add_argument('--disable-ipc-flooding-protection')

        # VPN/Proxy configuration
        proxy_config = self.get_current_proxy_config()
        if proxy_config:
            proxy_server = proxy_config['proxy_server']
            proxy_type = proxy_config.get('proxy_type', 'http')

            if proxy_type == 'socks5':
                options.add_argument(f'--proxy-server={proxy_server}')
            else:
                options.add_argument(f'--proxy-server={proxy_server}')

            print(f"🔒 Using proxy: {proxy_server}")

        # Additional stealth options (commented out problematic ones)
        # options.add_experimental_option("excludeSwitches", ["enable-automation", "enable-logging"])
        # options.add_experimental_option('useAutomationExtension', False)

        # Disable images and CSS for faster loading and less detection
        prefs = {
            "profile.managed_default_content_settings.images": 2,
            "profile.default_content_setting_values.notifications": 2,
            "profile.managed_default_content_settings.stylesheets": 2,
            "profile.managed_default_content_settings.cookies": 1,
            "profile.managed_default_content_settings.javascript": 1,
            "profile.managed_default_content_settings.plugins": 1,
            "profile.managed_default_content_settings.popups": 2,
            "profile.managed_default_content_settings.geolocation": 2,
            "profile.managed_default_content_settings.media_stream": 2,
        }
        options.add_experimental_option("prefs", prefs)

        if self.headless:
            options.add_argument('--headless')

        # Use a more realistic user agent
        ua = UserAgent()
        options.add_argument(f'--user-agent={ua.random}')

        try:
            self.driver = uc.Chrome(options=options, version_main=None)
        except Exception as e:
            print(f"Failed to create undetected Chrome driver: {e}")
            print("Falling back to regular Chrome driver...")
            self.driver = uc.Chrome(options=options)

        # Enhanced anti-detection scripts
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        self.driver.execute_script("Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]})")
        self.driver.execute_script("Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en']})")
        self.driver.execute_script("window.chrome = { runtime: {} }")

        # Set realistic window size
        self.driver.set_window_size(1366, 768)

    def detect_and_handle_captcha(self, skip_detection=False):
        """Detect and handle CAPTCHA challenges with improved detection"""
        if skip_detection:
            return True

        # More specific CAPTCHA indicators to reduce false positives
        captcha_indicators = [
            "i'm not a robot",
            "recaptcha",
            "verify you are human",
            "unusual traffic from your computer network",
            "automated queries",
            "security check required"
        ]

        page_source = self.driver.page_source.lower()

        # Check for specific CAPTCHA elements in the DOM
        captcha_elements = [
            "//iframe[contains(@src, 'recaptcha')]",
            "//div[@class='g-recaptcha']",
            "//*[contains(@class, 'captcha')]",
            "//*[contains(text(), \"I'm not a robot\")]"
        ]

        # First check for actual CAPTCHA elements
        captcha_found = False
        for xpath in captcha_elements:
            try:
                element = self.driver.find_element(By.XPATH, xpath)
                if element.is_displayed():
                    captcha_found = True
                    break
            except:
                continue

        # Then check page source for indicators (more restrictive)
        if not captcha_found:
            for indicator in captcha_indicators:
                if indicator in page_source and len(page_source) < 10000:  # Only check on smaller pages
                    captcha_found = True
                    break

        if captcha_found:
            print(f"🤖 CAPTCHA detected on page")
            print("⏳ Waiting for manual CAPTCHA resolution...")
            print("Please solve the CAPTCHA manually in the browser window.")
            print("💡 Tip: If no CAPTCHA is visible, press Ctrl+C to skip detection")

            # Wait for user to solve CAPTCHA (reduced to 1 minute)
            wait_time = 0
            max_wait = 60  # 1 minute

            while wait_time < max_wait:
                time.sleep(3)
                wait_time += 3

                # Check if CAPTCHA elements are still present
                captcha_still_present = False
                for xpath in captcha_elements:
                    try:
                        element = self.driver.find_element(By.XPATH, xpath)
                        if element.is_displayed():
                            captcha_still_present = True
                            break
                    except:
                        continue

                if not captcha_still_present:
                    print("✅ CAPTCHA appears to be resolved!")
                    return True

                if wait_time % 15 == 0:  # Print status every 15 seconds
                    print(f"⏳ Still waiting... ({wait_time}/{max_wait} seconds)")

            print("⚠️ CAPTCHA resolution timeout. Continuing anyway...")
            return False

        return True  # No CAPTCHA detected

    def smart_delay(self, min_delay=0.5, max_delay=1.5):
        """Add minimal delays for speed"""
        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)

    def fast_http_scrape(self, url, timeout=5):
        """Ethical HTTP-based scraping with respect for robots.txt and rate limiting"""
        try:
            # Check if domain is allowed for scraping
            if self.ethical_mode and not self.is_domain_allowed(url):
                print(f"🚫 Skipping {url} - not allowed by robots.txt or blocked")
                return set()

            # Apply rate limiting
            if self.ethical_mode:
                self.respect_rate_limit(url)

            # Rotate IP if needed
            self.rotate_ip_if_needed()

            # Update session with current proxy if using VPN
            if self.use_vpn and self.vpn_type == "proxy" and self.vpn_manager and self.vpn_manager.current_proxy:
                proxy_url = f"http://{self.vpn_manager.current_proxy['ip']}:{self.vpn_manager.current_proxy['port']}"
                self.session.proxies.update({
                    'http': proxy_url,
                    'https': proxy_url
                })

            # Add polite headers
            headers = {
                'User-Agent': 'Mozilla/5.0 (compatible; EmailScraper/1.0; +https://example.com/bot)',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }

            response = self.session.get(url, timeout=timeout, headers=headers)

            # Check for rate limiting responses
            if response.status_code == 429:  # Too Many Requests
                print(f"⚠️ Rate limited by {url}, backing off...")
                self.ethical_delay(30)  # Wait 30 seconds
                return set()
            elif response.status_code == 403:  # Forbidden
                print(f"🚫 Access forbidden to {url}")
                from urllib.parse import urlparse
                domain = urlparse(url).netloc
                self.blocked_domains.add(domain)
                return set()
            elif response.status_code == 200:
                # Apply ethical delay after successful request
                if self.ethical_mode:
                    self.ethical_delay()
                return self.extract_emails_from_text(response.text)

        except Exception as e:
            # If proxy fails, try without proxy (but still respect ethics)
            if self.use_vpn and 'proxy' in str(e).lower():
                try:
                    self.session.proxies.clear()
                    if self.ethical_mode:
                        self.ethical_delay()
                    response = self.session.get(url, timeout=timeout)
                    if response.status_code == 200:
                        return self.extract_emails_from_text(response.text)
                except:
                    pass
        return set()

    def extract_emails_from_text(self, text):
        """Extract emails from text content"""
        emails = set()
        matches = self.email_pattern.findall(text)
        for email in matches:
            if self.is_valid_email(email):
                emails.add(email.lower())
        return emails

    def is_valid_email(self, email):
        """Validate email format and filter out common false positives"""
        if not email or len(email) < 5:
            return False

        # Filter out common false positives
        invalid_patterns = [
            'example.com', 'test.com', 'domain.com', 'email.com',
            'yourname@', 'name@', 'user@', 'admin@localhost',
            '.png', '.jpg', '.gif', '.css', '.js', 'noreply@',
            'no-reply@', 'donotreply@', 'support@example'
        ]

        email_lower = email.lower()
        for pattern in invalid_patterns:
            if pattern in email_lower:
                return False

        return True

    def extract_phone_numbers(self, text):
        """Extract phone numbers from text"""
        phones = set()
        matches = self.phone_pattern.findall(text)
        for phone in matches:
            # Clean and validate phone number
            cleaned_phone = re.sub(r'[^\d+]', '', phone)
            if len(cleaned_phone) >= 10:  # Minimum valid phone length
                phones.add(phone.strip())
        return phones

    def extract_companies(self, text):
        """Extract company names from text"""
        companies = set()
        matches = self.company_pattern.findall(text)
        for company in matches:
            company = company.strip()
            if len(company) > 2 and company not in ['the', 'and', 'or', 'but', 'for', 'at']:
                companies.add(company)
        return companies

    def extract_linkedin_profiles(self, text):
        """Extract LinkedIn profile URLs"""
        profiles = set()
        matches = self.linkedin_pattern.findall(text)
        for profile in matches:
            profiles.add(f"linkedin.com/in/{profile}")
        return profiles

    def extract_locations(self, text):
        """Extract city and location information"""
        # Common location patterns
        location_patterns = [
            r'(?:in|from|based\s+in|located\s+in)\s+([A-Z][a-zA-Z\s]+(?:,\s*[A-Z]{2,})?)',
            r'([A-Z][a-zA-Z\s]+,\s*[A-Z]{2,3})',  # City, State/Country
            r'([A-Z][a-zA-Z\s]+,\s*[A-Z][a-zA-Z\s]+)'  # City, Country
        ]

        locations = set()
        for pattern in location_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for location in matches:
                location = location.strip()
                if len(location) > 3:
                    locations.add(location)
        return locations

    def human_like_scroll(self, pause_time=None):
        """Simulate human-like scrolling behavior"""
        if pause_time is None:
            pause_time = random.uniform(0.5, 2.0)
            
        # Get page height
        last_height = self.driver.execute_script("return document.body.scrollHeight")
        
        while True:
            # Random scroll amount
            scroll_amount = random.randint(200, 800)
            self.driver.execute_script(f"window.scrollBy(0, {scroll_amount});")
            
            # Random pause
            time.sleep(random.uniform(0.1, 0.5))
            
            # Check if reached bottom
            new_height = self.driver.execute_script("return document.body.scrollHeight")
            current_position = self.driver.execute_script("return window.pageYOffset + window.innerHeight")
            
            if current_position >= new_height:
                break
                
        time.sleep(pause_time)
        
    def extract_comprehensive_data_from_page(self, target_profession="", target_city="", target_country="", target_profession_aliases=None):
        """Extract comprehensive professional data from current page with enhanced profession matching"""
        page_source = self.driver.page_source

        # Extract all data types
        emails = self.extract_emails_from_text(page_source)
        phones = self.extract_phone_numbers(page_source)
        companies = self.extract_companies(page_source)
        linkedin_profiles = self.extract_linkedin_profiles(page_source)
        locations = self.extract_locations(page_source)

        # Create comprehensive contact records
        contacts_found = []

        # If we have emails, create detailed records
        for email in emails:
            contact = {
                'profession_name': target_profession,
                'email': email,
                'phone_number': '',
                'company_name': '',
                'city_name': target_city,
                'country_name': target_country,
                'linkedin_profile': '',
                'domain': email.split('@')[1] if '@' in email else '',
                'username': email.split('@')[0] if '@' in email else '',
                'full_name': '',
                'job_title': '',
                'industry': '',
                'experience_level': '',
                'skills': '',
                'website': '',
                'scraped_date': datetime.now().strftime("%Y-%m-%d"),
                'scraped_time': datetime.now().strftime("%H:%M:%S"),
                'source_url': self.driver.current_url if self.driver else '',
                'data_quality_score': 1,  # Base score for having email
                'profession_match_score': 0,
                'relevance_score': 0
            }

            # Try to match additional data to this email
            email_domain = email.split('@')[1] if '@' in email else ''

            # Enhanced company matching with domain correlation
            best_company_match = ""
            company_score = 0

            for company in companies:
                # Check if company domain matches email domain
                if email_domain.lower() in company.lower() or company.lower() in email_domain.lower():
                    best_company_match = company
                    company_score = 3
                    break
                # Check for partial matches
                elif any(part in company.lower() for part in email_domain.lower().split('.')):
                    if company_score < 2:
                        best_company_match = company
                        company_score = 2

            # If no good match, use first company found
            if not best_company_match and companies:
                best_company_match = list(companies)[0]
                company_score = 1

            contact['company_name'] = best_company_match
            contact['data_quality_score'] += company_score

            # Add phone number (first available)
            if phones:
                contact['phone_number'] = list(phones)[0]
                contact['data_quality_score'] += 2

            # Add LinkedIn profile (first available)
            if linkedin_profiles:
                contact['linkedin_profile'] = list(linkedin_profiles)[0]
                contact['data_quality_score'] += 2

            # Enhanced location matching
            best_location = ""
            if locations:
                # Prefer locations that match target city/country
                for location in locations:
                    if target_city.lower() in location.lower() or target_country.lower() in location.lower():
                        best_location = location
                        contact['data_quality_score'] += 2
                        break

                # If no match, use first location
                if not best_location:
                    best_location = list(locations)[0]
                    contact['data_quality_score'] += 1

                # Parse location
                if ',' in best_location:
                    parts = best_location.split(',')
                    contact['city_name'] = parts[0].strip()
                    if len(parts) > 1:
                        contact['country_name'] = parts[1].strip()

            # Extract enhanced context around email with profession matching
            email_context = self.extract_context_around_email(page_source, email, target_profession_aliases)
            if email_context:
                contact.update(email_context)

                # Add profession match score to data quality
                if 'profession_match_score' in email_context:
                    contact['data_quality_score'] += email_context['profession_match_score']

            # Calculate overall relevance score
            relevance_factors = [
                contact.get('profession_match_score', 0),
                2 if contact['company_name'] else 0,
                2 if contact['phone_number'] else 0,
                1 if contact['full_name'] else 0,
                1 if contact['job_title'] else 0,
                1 if contact['skills'] else 0
            ]
            contact['relevance_score'] = sum(relevance_factors)

            contacts_found.append(contact)

        # Sort contacts by relevance score (highest first)
        contacts_found.sort(key=lambda x: x['relevance_score'], reverse=True)

        return contacts_found

    def extract_emails_from_page(self) -> Set[str]:
        """Extract emails from current page - optimized version"""
        emails = set()

        # Direct regex search on page source (faster than BeautifulSoup)
        page_source = self.driver.page_source
        found_emails = self.email_pattern.findall(page_source)

        for email in found_emails:
            if self.is_valid_email(email):
                emails.add(email.lower())

        return emails

    def extract_context_around_email(self, text, email, target_profession_aliases=None):
        """Extract contextual information around an email address with profession matching"""
        context_data = {}

        # Find the email in text and get surrounding context
        email_index = text.lower().find(email.lower())
        if email_index == -1:
            return context_data

        # Get 1000 characters before and after email for better context
        start = max(0, email_index - 1000)
        end = min(len(text), email_index + len(email) + 1000)
        context = text[start:end]
        context_lower = context.lower()

        # Enhanced job title extraction with profession matching
        job_title_patterns = [
            r'(?:Senior|Lead|Principal|Chief|Head of|Director of|Manager|Specialist|Engineer|Developer|Analyst|Consultant|Expert|Staff|VP|Vice President)\s+[A-Za-z\s]+',
            r'[A-Za-z\s]+(?:Engineer|Developer|Manager|Director|Analyst|Specialist|Consultant|Expert|Lead|Senior|Architect|Scientist)',
            r'(?:Data|Software|DevOps|Machine Learning|AI|Cloud|Platform|Infrastructure|Backend|Frontend|Full Stack)\s+[A-Za-z\s]+',
        ]

        # Try to match profession-specific titles first
        if target_profession_aliases:
            for alias in target_profession_aliases:
                if alias.lower() in context_lower:
                    context_data['job_title'] = alias.title()
                    context_data['profession_match_score'] = 10  # High score for exact match
                    break

        # If no profession match, use general patterns
        if 'job_title' not in context_data:
            for pattern in job_title_patterns:
                matches = re.findall(pattern, context, re.IGNORECASE)
                if matches:
                    context_data['job_title'] = matches[0].strip()
                    context_data['profession_match_score'] = 5  # Medium score
                    break

        # Extract full names (enhanced patterns)
        name_patterns = [
            r'([A-Z][a-z]+\s+[A-Z][a-z]+)',  # First Last
            r'([A-Z][a-z]+\s+[A-Z]\.\s+[A-Z][a-z]+)',  # First M. Last
            r'([A-Z][a-z]+\s+[A-Z][a-z]+\s+[A-Z][a-z]+)',  # First Middle Last
        ]

        for pattern in name_patterns:
            matches = re.findall(pattern, context)
            if matches:
                # Filter out common false positives
                name = matches[0].strip()
                if not any(word in name.lower() for word in ['email', 'contact', 'phone', 'address', 'company']):
                    context_data['full_name'] = name
                    break

        # Enhanced experience level detection
        seniority_indicators = {
            'Senior': ['senior', 'sr.', 'lead', 'principal', 'staff', 'chief', 'head of', 'director', 'vp', 'vice president'],
            'Junior': ['junior', 'jr.', 'entry', 'associate', 'intern', 'trainee', 'graduate'],
            'Mid-level': ['mid', 'intermediate', 'regular', 'standard']
        }

        for level, indicators in seniority_indicators.items():
            if any(indicator in context_lower for indicator in indicators):
                context_data['experience_level'] = level
                break

        if 'experience_level' not in context_data:
            context_data['experience_level'] = 'Mid-level'  # Default

        # Extract skills and technologies
        tech_skills = [
            'python', 'java', 'scala', 'sql', 'spark', 'hadoop', 'kafka', 'airflow',
            'aws', 'azure', 'gcp', 'docker', 'kubernetes', 'terraform', 'jenkins',
            'react', 'angular', 'vue', 'node.js', 'django', 'flask', 'spring',
            'machine learning', 'deep learning', 'tensorflow', 'pytorch', 'scikit-learn',
            'tableau', 'power bi', 'looker', 'snowflake', 'redshift', 'bigquery'
        ]

        found_skills = []
        for skill in tech_skills:
            if skill.lower() in context_lower:
                found_skills.append(skill)

        if found_skills:
            context_data['skills'] = ', '.join(found_skills[:10])  # Limit to top 10

        # Extract industry information
        industries = [
            'fintech', 'healthcare', 'e-commerce', 'retail', 'banking', 'insurance',
            'technology', 'startup', 'enterprise', 'consulting', 'media', 'gaming',
            'automotive', 'aerospace', 'energy', 'telecommunications', 'education'
        ]

        for industry in industries:
            if industry in context_lower:
                context_data['industry'] = industry.title()
                break

        return context_data

    def fast_google_search(self, query, max_results=50):
        """Fast Google search using multiple search engines"""
        emails = set()

        # Multiple search engines for better coverage
        search_engines = [
            f"https://www.google.com/search?q={quote_plus(query)}&num=50",
            f"https://www.bing.com/search?q={quote_plus(query)}&count=50"
        ]

        with ThreadPoolExecutor(max_workers=2) as executor:
            futures = [executor.submit(self.fast_http_scrape, url) for url in search_engines]
            for future in as_completed(futures):
                try:
                    result_emails = future.result()
                    emails.update(result_emails)
                except:
                    continue

        return emails
        
    def scrape_google_search_comprehensive(self, query: str, target_profession="", target_city="", target_country="", target_profession_aliases=None, max_pages: int = 10):
        """Comprehensive Google search scraping with detailed data extraction and profession matching"""
        contacts_found = []

        search_url = f"https://www.google.com/search?q={query}&num=100"  # Get more results per page
        self.driver.get(search_url)

        # Quick cookie consent handling
        try:
            accept_button = WebDriverWait(self.driver, 2).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Accept') or contains(text(), 'I agree')]"))
            )
            accept_button.click()
        except:
            pass

        for page in range(max_pages):
            print(f"Scraping Google search page {page + 1}")

            # Extract comprehensive data from current page with profession aliases
            page_contacts = self.extract_comprehensive_data_from_page(
                target_profession, target_city, target_country, target_profession_aliases
            )
            contacts_found.extend(page_contacts)

            # Also extract emails for backward compatibility
            page_emails = self.extract_emails_from_page()
            self.emails.update(page_emails)

            print(f"Found {len(page_contacts)} detailed contacts and {len(page_emails)} emails on this page")

            # Minimal delay
            self.smart_delay(0.5, 1.0)

            # Try to go to next page quickly
            try:
                next_button = self.driver.find_element(By.ID, "pnnext")
                if next_button.is_enabled():
                    next_button.click()
                    self.smart_delay(1, 2)  # Minimal delay after clicking
                else:
                    break
            except Exception as e:
                print(f"No more pages or error navigating: {e}")
                break

        return contacts_found

    def scrape_google_search(self, query: str, max_pages: int = 10) -> Set[str]:
        """Fast Google search scraping with minimal delays - backward compatibility"""
        emails = set()

        search_url = f"https://www.google.com/search?q={query}&num=100"  # Get more results per page
        self.driver.get(search_url)

        # Quick cookie consent handling
        try:
            accept_button = WebDriverWait(self.driver, 2).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Accept') or contains(text(), 'I agree')]"))
            )
            accept_button.click()
        except:
            pass

        for page in range(max_pages):
            print(f"Scraping Google search page {page + 1}")

            # Extract emails from current page
            page_emails = self.extract_emails_from_page()
            emails.update(page_emails)
            print(f"Found {len(page_emails)} emails on this page")

            # Minimal delay
            self.smart_delay(0.5, 1.0)

            # Try to go to next page quickly
            try:
                next_button = self.driver.find_element(By.ID, "pnnext")
                if next_button.is_enabled():
                    next_button.click()
                    self.smart_delay(1, 2)  # Minimal delay after clicking
                else:
                    break
            except Exception as e:
                print(f"No more pages or error navigating: {e}")
                break

        return emails
        
    def scrape_website(self, url: str) -> Set[str]:
        """Scrape emails from a specific website with CAPTCHA handling"""
        emails = set()

        try:
            print(f"Scraping: {url}")
            self.driver.get(url)

            # Check for CAPTCHA after loading the website (if enabled)
            if self.enable_captcha_detection and not self.detect_and_handle_captcha():
                print(f"⚠️ CAPTCHA detected on {url}, but continuing...")

            self.smart_delay(3, 6)

            # Scroll and extract emails
            self.human_like_scroll()
            page_emails = self.extract_emails_from_page()
            emails.update(page_emails)
            print(f"Found {len(page_emails)} emails on {url}")

        except Exception as e:
            print(f"Error scraping {url}: {e}")

        return emails

    def parallel_fast_scrape(self, queries: List[str]) -> Set[str]:
        """Ethical parallel scraping using HTTP requests with respect for rate limits"""
        all_emails = set()

        print(f"🚀 Starting ethical parallel scraping for {len(queries)} queries...")

        if self.ethical_mode:
            print("🌱 Ethical mode: Implementing rate limiting and robots.txt compliance")

        # Create expanded query list for coverage (reduced for ethical scraping)
        expanded_queries = []
        for query in queries:
            if self.ethical_mode:
                # Fewer variations for ethical scraping
                expanded_queries.extend([
                    f"{query} email contact",
                    f"{query} directory",
                    f"{query} contact information",
                    f"{query} professional email"
                ])
            else:
                # Full expansion for non-ethical mode
                expanded_queries.extend([
                    f"{query} email contact",
                    f"{query} directory",
                    f"{query} contact information",
                    f"{query} professional email",
                    f"{query} business contact",
                    f"{query} email address",
                    f"{query} contact details",
                    f"{query} email list",
                    f"{query} contact directory",
                    f"{query} professional contact"
                ])

        # Use ThreadPoolExecutor for parallel HTTP requests
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = []

            for query in expanded_queries:
                # Submit multiple search engines per query for maximum coverage
                search_urls = [
                    f"https://www.google.com/search?q={quote_plus(query)}&num=100",
                    f"https://www.bing.com/search?q={quote_plus(query)}&count=50",
                    f"https://duckduckgo.com/?q={quote_plus(query)}",
                    f"https://search.yahoo.com/search?p={quote_plus(query)}"
                ]

                for url in search_urls:
                    futures.append(executor.submit(self.fast_http_scrape, url))

            # Collect results as they complete
            completed = 0
            for future in as_completed(futures):
                try:
                    result_emails = future.result()
                    all_emails.update(result_emails)
                    completed += 1

                    if completed % 10 == 0:
                        print(f"⚡ Processed {completed}/{len(futures)} requests, found {len(all_emails)} emails so far")

                except Exception:
                    completed += 1
                    continue

        print(f"✅ Parallel scraping completed! Found {len(all_emails)} unique emails")
        return all_emails

    def scrape_multiple_sources_comprehensive(self, sources: List[str], target_profession="", target_city="", target_country="", target_profession_aliases=None):
        """Comprehensive scraping from multiple sources with detailed data extraction and profession matching"""
        all_contacts = []
        all_emails = set()

        # Separate URLs from search queries
        urls = [source for source in sources if source.startswith("http")]
        queries = [source for source in sources if not source.startswith("http")]

        # Use parallel fast scraping for search queries
        if queries:
            print("🚀 Using parallel fast scraping for search queries...")
            fast_emails = self.parallel_fast_scrape(queries)
            all_emails.update(fast_emails)

            # Convert fast emails to contact format with basic profession matching
            for email in fast_emails:
                # Try to determine if email is relevant to target profession
                email_domain = email.split('@')[1] if '@' in email else ''
                profession_relevance = 0

                # Check if domain suggests tech/data company
                if any(keyword in email_domain.lower() for keyword in ['data', 'tech', 'analytics', 'ai', 'ml']):
                    profession_relevance = 2

                contact = {
                    'profession_name': target_profession,
                    'email': email,
                    'phone_number': '',
                    'company_name': email_domain,
                    'city_name': target_city,
                    'country_name': target_country,
                    'linkedin_profile': '',
                    'domain': email_domain,
                    'username': email.split('@')[0] if '@' in email else '',
                    'full_name': '',
                    'job_title': '',
                    'industry': '',
                    'experience_level': '',
                    'skills': '',
                    'website': '',
                    'scraped_date': datetime.now().strftime("%Y-%m-%d"),
                    'scraped_time': datetime.now().strftime("%H:%M:%S"),
                    'source_url': 'parallel_search',
                    'data_quality_score': 1 + profession_relevance,
                    'profession_match_score': profession_relevance,
                    'relevance_score': profession_relevance
                }
                all_contacts.append(contact)

        # Use browser scraping for comprehensive data extraction
        if len(all_emails) < 100 and queries:
            print("📈 Boosting results with comprehensive browser scraping...")
            # Use more queries for profession alias searches
            query_limit = min(5, len(queries))  # Increased from 3 to 5
            for query in queries[:query_limit]:
                try:
                    contacts = self.scrape_google_search_comprehensive(
                        query, target_profession, target_city, target_country,
                        target_profession_aliases, max_pages=3
                    )
                    all_contacts.extend(contacts)
                    print(f"Comprehensive scraping found {len(contacts)} detailed contacts from {query}")
                except Exception as e:
                    print(f"Error with comprehensive scraping {query}: {e}")
                    continue

        # Use browser scraping for direct URLs (if any)
        if urls:
            print("🌐 Scraping direct URLs with browser...")
            for url in urls:
                try:
                    emails = self.scrape_website(url)
                    all_emails.update(emails)
                    print(f"Total found from {url}: {len(emails)} emails")
                    self.smart_delay(1, 2)  # Minimal delay
                except Exception as e:
                    print(f"Error with URL {url}: {e}")
                    continue

        # Remove duplicates and sort by relevance
        unique_contacts = []
        seen_emails = set()

        for contact in all_contacts:
            if contact['email'] not in seen_emails:
                seen_emails.add(contact['email'])
                unique_contacts.append(contact)

        # Sort by relevance score (highest first)
        unique_contacts.sort(key=lambda x: x.get('relevance_score', 0), reverse=True)

        # Store contacts and emails
        self.contacts.extend(unique_contacts)
        self.emails.update(all_emails)

        print(f"📊 Total unique contacts found: {len(unique_contacts)}")
        print(f"📧 Total unique emails found: {len(all_emails)}")

        return unique_contacts

    def scrape_multiple_sources(self, sources: List[str]) -> Set[str]:
        """Fast scraping from multiple sources with hybrid approach - backward compatibility"""
        all_emails = set()

        # Separate URLs from search queries
        urls = [source for source in sources if source.startswith("http")]
        queries = [source for source in sources if not source.startswith("http")]

        # Use parallel fast scraping for search queries
        if queries:
            print("🚀 Using parallel fast scraping for search queries...")
            fast_emails = self.parallel_fast_scrape(queries)
            all_emails.update(fast_emails)

        # Use browser scraping for direct URLs (if any)
        if urls:
            print("🌐 Scraping direct URLs with browser...")
            for url in urls:
                try:
                    emails = self.scrape_website(url)
                    all_emails.update(emails)
                    print(f"Total found from {url}: {len(emails)} emails")
                    self.smart_delay(1, 2)  # Minimal delay
                except Exception as e:
                    print(f"Error with URL {url}: {e}")
                    continue

        # If we have few emails, fall back to browser scraping for some queries
        if len(all_emails) < 50 and queries:
            print("📈 Boosting results with browser scraping...")
            for query in queries[:2]:  # Only first 2 queries to save time
                try:
                    emails = self.scrape_google_search(query, max_pages=3)
                    all_emails.update(emails)
                    print(f"Browser scraping found {len(emails)} additional emails from {query}")
                except Exception as e:
                    print(f"Error with browser scraping {query}: {e}")
                    continue

        return all_emails
        
    def save_emails_multiple_formats(self, base_filename="scraped_contacts", formats=None):
        """Save scraped contacts and emails in multiple formats with comprehensive data"""
        if formats is None:
            formats = ['json']  # Default format

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Ensure output directory exists
        if not os.path.exists(self.output_folder):
            self.create_output_directory()

        # Prepare comprehensive contact data
        contact_data = []

        # Use comprehensive contacts if available, otherwise fall back to emails
        if self.contacts:
            # Remove duplicates based on email
            seen_emails = set()
            unique_contacts = []
            for contact in self.contacts:
                if contact['email'] not in seen_emails:
                    seen_emails.add(contact['email'])
                    unique_contacts.append(contact)

            for i, contact in enumerate(unique_contacts, 1):
                contact_record = {
                    'id': i,
                    'profession_name': contact.get('profession_name', ''),
                    'email': contact.get('email', ''),
                    'phone_number': contact.get('phone_number', ''),
                    'company_name': contact.get('company_name', ''),
                    'city_name': contact.get('city_name', ''),
                    'country_name': contact.get('country_name', ''),
                    'linkedin_profile': contact.get('linkedin_profile', ''),
                    'domain': contact.get('domain', ''),
                    'username': contact.get('username', ''),
                    'full_name': contact.get('full_name', ''),
                    'job_title': contact.get('job_title', ''),
                    'industry': contact.get('industry', ''),
                    'experience_level': contact.get('experience_level', ''),
                    'skills': contact.get('skills', ''),
                    'website': contact.get('website', ''),
                    'scraped_date': contact.get('scraped_date', datetime.now().strftime("%Y-%m-%d")),
                    'scraped_time': contact.get('scraped_time', datetime.now().strftime("%H:%M:%S")),
                    'source_url': contact.get('source_url', ''),
                    'data_quality_score': contact.get('data_quality_score', 1)
                }
                contact_data.append(contact_record)
        else:
            # Fallback to email-only data
            email_list = list(self.emails)
            for i, email in enumerate(email_list, 1):
                contact_record = {
                    'id': i,
                    'profession_name': '',
                    'email': email,
                    'phone_number': '',
                    'company_name': email.split('@')[1] if '@' in email else '',
                    'city_name': '',
                    'country_name': '',
                    'linkedin_profile': '',
                    'domain': email.split('@')[1] if '@' in email else '',
                    'username': email.split('@')[0] if '@' in email else '',
                    'full_name': '',
                    'job_title': '',
                    'industry': '',
                    'experience_level': '',
                    'skills': '',
                    'website': '',
                    'scraped_date': datetime.now().strftime("%Y-%m-%d"),
                    'scraped_time': datetime.now().strftime("%H:%M:%S"),
                    'source_url': '',
                    'data_quality_score': 1
                }
                contact_data.append(contact_record)

        saved_files = []

        # Save in JSON format
        if 'json' in formats:
            json_filename = os.path.join(self.output_folder, f"{base_filename}_{timestamp}.json")
            json_data = {
                "metadata": {
                    "total_count": len(contact_data),
                    "total_emails": len(self.emails),
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "scraping_method": "Advanced Email Scraper - Comprehensive Data"
                },
                "contacts": contact_data
            }

            with open(json_filename, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, indent=2, ensure_ascii=False)
            saved_files.append(json_filename)
            print(f"✅ Saved {len(contact_data)} contacts to {json_filename}")

        # Save in CSV format
        if 'csv' in formats:
            csv_filename = os.path.join(self.output_folder, f"{base_filename}_{timestamp}.csv")
            df = pd.DataFrame(contact_data)
            df.to_csv(csv_filename, index=False, encoding='utf-8')
            saved_files.append(csv_filename)
            print(f"✅ Saved {len(contact_data)} contacts to {csv_filename}")

        # Save in Excel format
        if 'excel' in formats or 'xlsx' in formats:
            excel_filename = os.path.join(self.output_folder, f"{base_filename}_{timestamp}.xlsx")
            df = pd.DataFrame(contact_data)

            with pd.ExcelWriter(excel_filename, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='Emails', index=False)

                # Add summary sheet
                summary_data = {
                    'Metric': ['Total Emails', 'Unique Domains', 'Scraping Date', 'Scraping Time'],
                    'Value': [
                        len(self.emails),
                        len(set(email.split('@')[1] for email in self.emails if '@' in email)),
                        datetime.now().strftime("%Y-%m-%d"),
                        datetime.now().strftime("%H:%M:%S")
                    ]
                }
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='Summary', index=False)

            saved_files.append(excel_filename)
            print(f"✅ Saved {len(self.emails)} emails to {excel_filename}")

        # Save in TXT format (simple list)
        if 'txt' in formats:
            txt_filename = os.path.join(self.output_folder, f"{base_filename}_{timestamp}.txt")
            with open(txt_filename, 'w', encoding='utf-8') as f:
                f.write(f"# Scraped Professional Contacts - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"# Total Count: {len(contact_data)}\n")
                f.write(f"# Saved in: {self.output_folder}\n\n")
                for contact in contact_data:
                    f.write(f"Email: {contact['email']}\n")
                    if contact['full_name']:
                        f.write(f"Name: {contact['full_name']}\n")
                    if contact['job_title']:
                        f.write(f"Title: {contact['job_title']}\n")
                    if contact['company_name']:
                        f.write(f"Company: {contact['company_name']}\n")
                    if contact['phone_number']:
                        f.write(f"Phone: {contact['phone_number']}\n")
                    f.write("-" * 40 + "\n")
            saved_files.append(txt_filename)
            print(f"✅ Saved {len(contact_data)} contacts to {txt_filename}")

        # Save in XML format
        if 'xml' in formats:
            xml_filename = os.path.join(self.output_folder, f"{base_filename}_{timestamp}.xml")
            with open(xml_filename, 'w', encoding='utf-8') as f:
                f.write('<?xml version="1.0" encoding="UTF-8"?>\n')
                f.write('<contacts>\n')
                f.write(f'  <metadata>\n')
                f.write(f'    <total_count>{len(contact_data)}</total_count>\n')
                f.write(f'    <timestamp>{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</timestamp>\n')
                f.write(f'    <output_folder>{self.output_folder}</output_folder>\n')
                f.write(f'  </metadata>\n')
                f.write(f'  <contact_list>\n')
                for contact in contact_data:
                    f.write(f'    <contact id="{contact["id"]}">\n')
                    f.write(f'      <profession_name>{contact["profession_name"]}</profession_name>\n')
                    f.write(f'      <email>{contact["email"]}</email>\n')
                    f.write(f'      <phone_number>{contact["phone_number"]}</phone_number>\n')
                    f.write(f'      <company_name>{contact["company_name"]}</company_name>\n')
                    f.write(f'      <city_name>{contact["city_name"]}</city_name>\n')
                    f.write(f'      <country_name>{contact["country_name"]}</country_name>\n')
                    f.write(f'      <full_name>{contact["full_name"]}</full_name>\n')
                    f.write(f'      <job_title>{contact["job_title"]}</job_title>\n')
                    f.write(f'    </contact>\n')
                f.write(f'  </contact_list>\n')
                f.write('</contacts>\n')
            saved_files.append(xml_filename)
            print(f"✅ Saved {len(contact_data)} contacts to {xml_filename}")

        # Save in HTML format (table view)
        if 'html' in formats:
            html_filename = os.path.join(self.output_folder, f"{base_filename}_{timestamp}.html")
            with open(html_filename, 'w', encoding='utf-8') as f:
                f.write('<!DOCTYPE html>\n<html>\n<head>\n')
                f.write('<title>Professional Contacts Database</title>\n')
                f.write('<style>\n')
                f.write('table { border-collapse: collapse; width: 100%; font-family: Arial, sans-serif; }\n')
                f.write('th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n')
                f.write('th { background-color: #f2f2f2; font-weight: bold; }\n')
                f.write('tr:nth-child(even) { background-color: #f9f9f9; }\n')
                f.write('</style>\n</head>\n<body>\n')
                f.write(f'<h1>Professional Contacts Database</h1>\n')
                f.write(f'<p>Generated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>\n')
                f.write(f'<p>Total Contacts: {len(contact_data)}</p>\n')
                f.write('<table>\n')
                f.write('<tr><th>ID</th><th>Profession</th><th>Email</th><th>Phone</th><th>Company</th><th>Name</th><th>Title</th><th>City</th><th>Country</th></tr>\n')
                for contact in contact_data:
                    f.write(f'<tr>')
                    f.write(f'<td>{contact["id"]}</td>')
                    f.write(f'<td>{contact["profession_name"]}</td>')
                    f.write(f'<td>{contact["email"]}</td>')
                    f.write(f'<td>{contact["phone_number"]}</td>')
                    f.write(f'<td>{contact["company_name"]}</td>')
                    f.write(f'<td>{contact["full_name"]}</td>')
                    f.write(f'<td>{contact["job_title"]}</td>')
                    f.write(f'<td>{contact["city_name"]}</td>')
                    f.write(f'<td>{contact["country_name"]}</td>')
                    f.write(f'</tr>\n')
                f.write('</table>\n</body>\n</html>')
            saved_files.append(html_filename)
            print(f"✅ Saved {len(contact_data)} contacts to {html_filename}")

        # Generate Google Sheets import instructions
        if 'google-sheets' in formats:
            gs_filename = os.path.join(self.output_folder, f"{base_filename}_{timestamp}_google_sheets_import.csv")
            df = pd.DataFrame(contact_data)
            df.to_csv(gs_filename, index=False, encoding='utf-8')
            saved_files.append(gs_filename)

            # Create instructions file
            instructions_filename = os.path.join(self.output_folder, f"{base_filename}_{timestamp}_google_sheets_instructions.txt")
            with open(instructions_filename, 'w', encoding='utf-8') as f:
                f.write("📊 GOOGLE SHEETS IMPORT INSTRUCTIONS\n")
                f.write("=" * 50 + "\n\n")
                f.write("COMPREHENSIVE PROFESSIONAL CONTACTS DATABASE\n")
                f.write("This file contains detailed professional information including:\n")
                f.write("• Profession Name • Email • Phone Number • Company Name\n")
                f.write("• City • Country • Full Name • Job Title • LinkedIn Profile\n")
                f.write("• Industry • Experience Level • Skills • Data Quality Score\n\n")
                f.write(f"📁 Files Location: {self.output_folder}\n\n")
                f.write("IMPORT INSTRUCTIONS:\n")
                f.write("1. Open Google Sheets (sheets.google.com)\n")
                f.write("2. Create a new spreadsheet\n")
                f.write("3. Go to File > Import\n")
                f.write("4. Choose 'Upload' tab\n")
                f.write(f"5. Upload the file: {os.path.basename(gs_filename)}\n")
                f.write("6. Select 'Replace spreadsheet' and click 'Import data'\n\n")
                f.write("Alternative method:\n")
                f.write("1. Open the CSV file in Excel\n")
                f.write("2. Copy all data (Ctrl+A, then Ctrl+C)\n")
                f.write("3. Paste into Google Sheets (Ctrl+V)\n\n")
                f.write(f"Total contacts to import: {len(contact_data)}\n")
                f.write(f"Unique emails: {len(set(c['email'] for c in contact_data))}\n")
                f.write(f"Files saved in: {os.path.abspath(self.output_folder)}\n")

            saved_files.append(instructions_filename)
            print(f"✅ Saved {len(contact_data)} contacts for Google Sheets import: {gs_filename}")
            print(f"📋 Import instructions saved: {instructions_filename}")

        print(f"\n📁 Total files saved: {len(saved_files)} in folder: {self.output_folder}")
        print(f"📂 Full path: {os.path.abspath(self.output_folder)}")
        return saved_files
        
    def run_scraper_comprehensive(self, sources: List[str], target_profession="", target_city="", target_country="", target_profession_aliases=None, save_formats=['json']):
        """Main comprehensive scraper execution with detailed data extraction and profession matching"""
        try:
            self.setup_driver()
            print("Starting comprehensive professional data scraping...")
            print(f"🎯 Target Profession: {target_profession}")
            if target_profession_aliases:
                print(f"📋 Using {len(target_profession_aliases)} profession aliases for enhanced matching")

            # Use comprehensive scraping method with profession aliases
            scraped_contacts = self.scrape_multiple_sources_comprehensive(
                sources, target_profession, target_city, target_country, target_profession_aliases
            )

            print(f"✅ Total contacts found: {len(self.contacts)}")
            print(f"📧 Total emails found: {len(self.emails)}")

            # Show top contacts by relevance
            if self.contacts:
                print("\n🏆 TOP 5 MOST RELEVANT CONTACTS:")
                top_contacts = sorted(self.contacts, key=lambda x: x.get('relevance_score', 0), reverse=True)[:5]
                for i, contact in enumerate(top_contacts, 1):
                    print(f"   {i}. {contact['email']} - {contact.get('job_title', 'N/A')} at {contact.get('company_name', 'N/A')} (Score: {contact.get('relevance_score', 0)})")

            # Save in multiple formats
            if self.contacts or self.emails:
                self.save_emails_multiple_formats(formats=save_formats)
                print(f"📁 Professional data saved in {len(save_formats)} format(s): {', '.join(save_formats)}")
            else:
                print("⚠️ No contacts or emails found to save")

        finally:
            if self.driver:
                self.driver.quit()

    def run_scraper(self, sources: List[str], save_formats=['json']):
        """Main scraper execution with configurable save formats - backward compatibility"""
        try:
            self.setup_driver()
            print("Starting email scraping...")

            scraped_emails = self.scrape_multiple_sources(sources)
            self.emails.update(scraped_emails)

            print(f"Total emails found: {len(self.emails)}")

            # Save in multiple formats
            if self.emails:
                self.save_emails_multiple_formats(formats=save_formats)
                print(f"📁 Emails saved in {len(save_formats)} format(s): {', '.join(save_formats)}")
            else:
                print("⚠️ No emails found to save")

        finally:
            if self.driver:
                self.driver.quit()