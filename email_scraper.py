import time
import random
import re
import json
from typing import Set, List
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import <PERSON><PERSON>hains
from selenium.webdriver.chrome.options import Options
import undetected_chromedriver as uc
from bs4 import BeautifulSoup
from fake_useragent import UserAgent
import requests

class AdvancedEmailScraper:
    def __init__(self, headless=False):
        self.emails = set()
        self.visited_urls = set()
        self.driver = None
        self.headless = headless
        self.email_pattern = re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')
        
    def setup_driver(self):
        """Setup undetected Chrome driver with anti-detection measures"""
        options = uc.ChromeOptions()

        # Basic options for compatibility
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-blink-features=AutomationControlled')

        if self.headless:
            options.add_argument('--headless')

        # Random user agent
        ua = UserAgent()
        options.add_argument(f'--user-agent={ua.random}')

        self.driver = uc.Chrome(options=options)
        
        # Execute script to remove webdriver property
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
    def human_like_scroll(self, pause_time=None):
        """Simulate human-like scrolling behavior"""
        if pause_time is None:
            pause_time = random.uniform(0.5, 2.0)
            
        # Get page height
        last_height = self.driver.execute_script("return document.body.scrollHeight")
        
        while True:
            # Random scroll amount
            scroll_amount = random.randint(200, 800)
            self.driver.execute_script(f"window.scrollBy(0, {scroll_amount});")
            
            # Random pause
            time.sleep(random.uniform(0.1, 0.5))
            
            # Check if reached bottom
            new_height = self.driver.execute_script("return document.body.scrollHeight")
            current_position = self.driver.execute_script("return window.pageYOffset + window.innerHeight")
            
            if current_position >= new_height:
                break
                
        time.sleep(pause_time)
        
    def extract_emails_from_page(self) -> Set[str]:
        """Extract emails from current page"""
        emails = set()
        
        # Get page source
        soup = BeautifulSoup(self.driver.page_source, 'html.parser')
        
        # Extract from text content
        text_content = soup.get_text()
        found_emails = self.email_pattern.findall(text_content)
        emails.update(found_emails)
        
        # Extract from mailto links
        mailto_links = soup.find_all('a', href=re.compile(r'^mailto:'))
        for link in mailto_links:
            href = link.get('href', '')
            email = href.replace('mailto:', '').split('?')[0]
            if self.email_pattern.match(email):
                emails.add(email)
                
        return emails
        
    def scrape_google_search(self, query: str, max_pages: int = 5) -> Set[str]:
        """Scrape emails from Google search results"""
        emails = set()
        
        search_url = f"https://www.google.com/search?q={query}"
        self.driver.get(search_url)
        
        # Handle cookie consent
        try:
            accept_button = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Accept') or contains(text(), 'I agree')]"))
            )
            accept_button.click()
        except:
            pass
            
        for page in range(max_pages):
            print(f"Scraping Google search page {page + 1}")
            
            # Extract emails from current page
            page_emails = self.extract_emails_from_page()
            emails.update(page_emails)
            print(f"Found {len(page_emails)} emails on this page")
            
            # Scroll and simulate human behavior
            self.human_like_scroll()
            
            # Try to go to next page
            try:
                next_button = self.driver.find_element(By.ID, "pnnext")
                if next_button.is_enabled():
                    next_button.click()
                    time.sleep(random.uniform(2, 5))
                else:
                    break
            except:
                break
                
        return emails
        
    def scrape_website(self, url: str) -> Set[str]:
        """Scrape emails from a specific website"""
        emails = set()
        
        try:
            print(f"Scraping: {url}")
            self.driver.get(url)
            time.sleep(random.uniform(2, 5))
            
            # Scroll and extract emails
            self.human_like_scroll()
            page_emails = self.extract_emails_from_page()
            emails.update(page_emails)
            print(f"Found {len(page_emails)} emails on {url}")
                    
        except Exception as e:
            print(f"Error scraping {url}: {e}")
            
        return emails
        
    def scrape_multiple_sources(self, sources: List[str]) -> Set[str]:
        """Scrape emails from multiple sources"""
        all_emails = set()
        
        for source in sources:
            try:
                if source.startswith("http"):
                    # Direct website
                    emails = self.scrape_website(source)
                else:
                    # Google search query
                    emails = self.scrape_google_search(source)
                    
                all_emails.update(emails)
                print(f"Total found from {source}: {len(emails)} emails")
                
                # Random delay between sources
                time.sleep(random.uniform(5, 10))
                
            except Exception as e:
                print(f"Error with source {source}: {e}")
                continue
                
        return all_emails
        
    def save_emails(self, filename: str = "scraped_emails.json"):
        """Save scraped emails to file"""
        email_data = {
            "emails": list(self.emails),
            "total_count": len(self.emails),
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        with open(filename, 'w') as f:
            json.dump(email_data, f, indent=2)
            
        print(f"Saved {len(self.emails)} emails to {filename}")
        
    def run_scraper(self, sources: List[str]):
        """Main scraper execution"""
        try:
            self.setup_driver()
            print("Starting email scraping...")
            
            scraped_emails = self.scrape_multiple_sources(sources)
            self.emails.update(scraped_emails)
            
            print(f"Total emails found: {len(self.emails)}")
            self.save_emails()
            
        finally:
            if self.driver:
                self.driver.quit()