import time
import random
import re
import json
import csv
import asyncio
import aiohttp
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
from typing import Set, List
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON>river<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.chrome.options import Options
import undetected_chromedriver as uc
from bs4 import BeautifulSoup
from fake_useragent import UserAgent
import requests
from urllib.parse import quote_plus
import pandas as pd
from datetime import datetime

class AdvancedEmailScraper:
    def __init__(self, headless=False, enable_captcha_detection=True, max_workers=5):
        self.emails = set()
        self.visited_urls = set()
        self.driver = None
        self.headless = headless
        self.enable_captcha_detection = enable_captcha_detection
        self.max_workers = max_workers
        self.email_pattern = re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
    def setup_driver(self):
        """Setup undetected Chrome driver with enhanced anti-detection measures"""
        options = uc.ChromeOptions()

        # Enhanced anti-detection options
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-plugins-discovery')
        options.add_argument('--disable-web-security')
        options.add_argument('--disable-features=VizDisplayCompositor')
        options.add_argument('--disable-ipc-flooding-protection')

        # Additional stealth options (commented out problematic ones)
        # options.add_experimental_option("excludeSwitches", ["enable-automation", "enable-logging"])
        # options.add_experimental_option('useAutomationExtension', False)

        # Disable images and CSS for faster loading and less detection
        prefs = {
            "profile.managed_default_content_settings.images": 2,
            "profile.default_content_setting_values.notifications": 2,
            "profile.managed_default_content_settings.stylesheets": 2,
            "profile.managed_default_content_settings.cookies": 1,
            "profile.managed_default_content_settings.javascript": 1,
            "profile.managed_default_content_settings.plugins": 1,
            "profile.managed_default_content_settings.popups": 2,
            "profile.managed_default_content_settings.geolocation": 2,
            "profile.managed_default_content_settings.media_stream": 2,
        }
        options.add_experimental_option("prefs", prefs)

        if self.headless:
            options.add_argument('--headless')

        # Use a more realistic user agent
        ua = UserAgent()
        options.add_argument(f'--user-agent={ua.random}')

        try:
            self.driver = uc.Chrome(options=options, version_main=None)
        except Exception as e:
            print(f"Failed to create undetected Chrome driver: {e}")
            print("Falling back to regular Chrome driver...")
            self.driver = uc.Chrome(options=options)

        # Enhanced anti-detection scripts
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        self.driver.execute_script("Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]})")
        self.driver.execute_script("Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en']})")
        self.driver.execute_script("window.chrome = { runtime: {} }")

        # Set realistic window size
        self.driver.set_window_size(1366, 768)

    def detect_and_handle_captcha(self, skip_detection=False):
        """Detect and handle CAPTCHA challenges with improved detection"""
        if skip_detection:
            return True

        # More specific CAPTCHA indicators to reduce false positives
        captcha_indicators = [
            "i'm not a robot",
            "recaptcha",
            "verify you are human",
            "unusual traffic from your computer network",
            "automated queries",
            "security check required"
        ]

        page_source = self.driver.page_source.lower()

        # Check for specific CAPTCHA elements in the DOM
        captcha_elements = [
            "//iframe[contains(@src, 'recaptcha')]",
            "//div[@class='g-recaptcha']",
            "//*[contains(@class, 'captcha')]",
            "//*[contains(text(), \"I'm not a robot\")]"
        ]

        # First check for actual CAPTCHA elements
        captcha_found = False
        for xpath in captcha_elements:
            try:
                element = self.driver.find_element(By.XPATH, xpath)
                if element.is_displayed():
                    captcha_found = True
                    break
            except:
                continue

        # Then check page source for indicators (more restrictive)
        if not captcha_found:
            for indicator in captcha_indicators:
                if indicator in page_source and len(page_source) < 10000:  # Only check on smaller pages
                    captcha_found = True
                    break

        if captcha_found:
            print(f"🤖 CAPTCHA detected on page")
            print("⏳ Waiting for manual CAPTCHA resolution...")
            print("Please solve the CAPTCHA manually in the browser window.")
            print("💡 Tip: If no CAPTCHA is visible, press Ctrl+C to skip detection")

            # Wait for user to solve CAPTCHA (reduced to 1 minute)
            wait_time = 0
            max_wait = 60  # 1 minute

            while wait_time < max_wait:
                time.sleep(3)
                wait_time += 3

                # Check if CAPTCHA elements are still present
                captcha_still_present = False
                for xpath in captcha_elements:
                    try:
                        element = self.driver.find_element(By.XPATH, xpath)
                        if element.is_displayed():
                            captcha_still_present = True
                            break
                    except:
                        continue

                if not captcha_still_present:
                    print("✅ CAPTCHA appears to be resolved!")
                    return True

                if wait_time % 15 == 0:  # Print status every 15 seconds
                    print(f"⏳ Still waiting... ({wait_time}/{max_wait} seconds)")

            print("⚠️ CAPTCHA resolution timeout. Continuing anyway...")
            return False

        return True  # No CAPTCHA detected

    def smart_delay(self, min_delay=0.5, max_delay=1.5):
        """Add minimal delays for speed"""
        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)

    def fast_http_scrape(self, url, timeout=5):
        """Fast HTTP-based scraping without browser"""
        try:
            response = self.session.get(url, timeout=timeout)
            if response.status_code == 200:
                return self.extract_emails_from_text(response.text)
        except:
            pass
        return set()

    def extract_emails_from_text(self, text):
        """Extract emails from text content"""
        emails = set()
        matches = self.email_pattern.findall(text)
        for email in matches:
            if self.is_valid_email(email):
                emails.add(email.lower())
        return emails

    def is_valid_email(self, email):
        """Validate email format and filter out common false positives"""
        if not email or len(email) < 5:
            return False

        # Filter out common false positives
        invalid_patterns = [
            'example.com', 'test.com', 'domain.com', 'email.com',
            'yourname@', 'name@', 'user@', 'admin@localhost',
            '.png', '.jpg', '.gif', '.css', '.js'
        ]

        email_lower = email.lower()
        for pattern in invalid_patterns:
            if pattern in email_lower:
                return False

        return True

    def human_like_scroll(self, pause_time=None):
        """Simulate human-like scrolling behavior"""
        if pause_time is None:
            pause_time = random.uniform(0.5, 2.0)
            
        # Get page height
        last_height = self.driver.execute_script("return document.body.scrollHeight")
        
        while True:
            # Random scroll amount
            scroll_amount = random.randint(200, 800)
            self.driver.execute_script(f"window.scrollBy(0, {scroll_amount});")
            
            # Random pause
            time.sleep(random.uniform(0.1, 0.5))
            
            # Check if reached bottom
            new_height = self.driver.execute_script("return document.body.scrollHeight")
            current_position = self.driver.execute_script("return window.pageYOffset + window.innerHeight")
            
            if current_position >= new_height:
                break
                
        time.sleep(pause_time)
        
    def extract_emails_from_page(self) -> Set[str]:
        """Extract emails from current page - optimized version"""
        emails = set()

        # Direct regex search on page source (faster than BeautifulSoup)
        page_source = self.driver.page_source
        found_emails = self.email_pattern.findall(page_source)

        for email in found_emails:
            if self.is_valid_email(email):
                emails.add(email.lower())

        return emails

    def fast_google_search(self, query, max_results=50):
        """Fast Google search using multiple search engines"""
        emails = set()

        # Multiple search engines for better coverage
        search_engines = [
            f"https://www.google.com/search?q={quote_plus(query)}&num=50",
            f"https://www.bing.com/search?q={quote_plus(query)}&count=50"
        ]

        with ThreadPoolExecutor(max_workers=2) as executor:
            futures = [executor.submit(self.fast_http_scrape, url) for url in search_engines]
            for future in as_completed(futures):
                try:
                    result_emails = future.result()
                    emails.update(result_emails)
                except:
                    continue

        return emails
        
    def scrape_google_search(self, query: str, max_pages: int = 10) -> Set[str]:
        """Fast Google search scraping with minimal delays"""
        emails = set()

        search_url = f"https://www.google.com/search?q={query}&num=100"  # Get more results per page
        self.driver.get(search_url)

        # Quick cookie consent handling
        try:
            accept_button = WebDriverWait(self.driver, 2).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Accept') or contains(text(), 'I agree')]"))
            )
            accept_button.click()
        except:
            pass

        for page in range(max_pages):
            print(f"Scraping Google search page {page + 1}")

            # Extract emails from current page
            page_emails = self.extract_emails_from_page()
            emails.update(page_emails)
            print(f"Found {len(page_emails)} emails on this page")

            # Minimal delay
            self.smart_delay(0.5, 1.0)

            # Try to go to next page quickly
            try:
                next_button = self.driver.find_element(By.ID, "pnnext")
                if next_button.is_enabled():
                    next_button.click()
                    self.smart_delay(1, 2)  # Minimal delay after clicking
                else:
                    break
            except Exception as e:
                print(f"No more pages or error navigating: {e}")
                break

        return emails
        
    def scrape_website(self, url: str) -> Set[str]:
        """Scrape emails from a specific website with CAPTCHA handling"""
        emails = set()

        try:
            print(f"Scraping: {url}")
            self.driver.get(url)

            # Check for CAPTCHA after loading the website (if enabled)
            if self.enable_captcha_detection and not self.detect_and_handle_captcha():
                print(f"⚠️ CAPTCHA detected on {url}, but continuing...")

            self.smart_delay(3, 6)

            # Scroll and extract emails
            self.human_like_scroll()
            page_emails = self.extract_emails_from_page()
            emails.update(page_emails)
            print(f"Found {len(page_emails)} emails on {url}")

        except Exception as e:
            print(f"Error scraping {url}: {e}")

        return emails

    def parallel_fast_scrape(self, queries: List[str]) -> Set[str]:
        """Parallel scraping using HTTP requests for maximum speed"""
        all_emails = set()

        print(f"🚀 Starting parallel fast scraping for {len(queries)} queries...")

        # Create massively expanded query list for maximum coverage
        expanded_queries = []
        for query in queries:
            expanded_queries.extend([
                f"{query} email contact",
                f"{query} directory",
                f"{query} contact information",
                f"{query} professional email",
                f"{query} business contact",
                f"{query} email address",
                f"{query} contact details",
                f"{query} email list",
                f"{query} contact directory",
                f"{query} professional contact"
            ])

        # Use ThreadPoolExecutor for parallel HTTP requests
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = []

            for query in expanded_queries:
                # Submit multiple search engines per query for maximum coverage
                search_urls = [
                    f"https://www.google.com/search?q={quote_plus(query)}&num=100",
                    f"https://www.bing.com/search?q={quote_plus(query)}&count=50",
                    f"https://duckduckgo.com/?q={quote_plus(query)}",
                    f"https://search.yahoo.com/search?p={quote_plus(query)}"
                ]

                for url in search_urls:
                    futures.append(executor.submit(self.fast_http_scrape, url))

            # Collect results as they complete
            completed = 0
            for future in as_completed(futures):
                try:
                    result_emails = future.result()
                    all_emails.update(result_emails)
                    completed += 1

                    if completed % 10 == 0:
                        print(f"⚡ Processed {completed}/{len(futures)} requests, found {len(all_emails)} emails so far")

                except Exception:
                    completed += 1
                    continue

        print(f"✅ Parallel scraping completed! Found {len(all_emails)} unique emails")
        return all_emails

    def scrape_multiple_sources(self, sources: List[str]) -> Set[str]:
        """Fast scraping from multiple sources with hybrid approach"""
        all_emails = set()

        # Separate URLs from search queries
        urls = [source for source in sources if source.startswith("http")]
        queries = [source for source in sources if not source.startswith("http")]

        # Use parallel fast scraping for search queries
        if queries:
            print("🚀 Using parallel fast scraping for search queries...")
            fast_emails = self.parallel_fast_scrape(queries)
            all_emails.update(fast_emails)

        # Use browser scraping for direct URLs (if any)
        if urls:
            print("🌐 Scraping direct URLs with browser...")
            for url in urls:
                try:
                    emails = self.scrape_website(url)
                    all_emails.update(emails)
                    print(f"Total found from {url}: {len(emails)} emails")
                    self.smart_delay(1, 2)  # Minimal delay
                except Exception as e:
                    print(f"Error with URL {url}: {e}")
                    continue

        # If we have few emails, fall back to browser scraping for some queries
        if len(all_emails) < 50 and queries:
            print("📈 Boosting results with browser scraping...")
            for query in queries[:2]:  # Only first 2 queries to save time
                try:
                    emails = self.scrape_google_search(query, max_pages=3)
                    all_emails.update(emails)
                    print(f"Browser scraping found {len(emails)} additional emails from {query}")
                except Exception as e:
                    print(f"Error with browser scraping {query}: {e}")
                    continue

        return all_emails
        
    def save_emails_multiple_formats(self, base_filename="scraped_emails", formats=None):
        """Save scraped emails in multiple formats"""
        if formats is None:
            formats = ['json']  # Default format

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        email_list = list(self.emails)

        # Prepare email data with additional metadata
        email_data = []
        for i, email in enumerate(email_list, 1):
            email_data.append({
                'id': i,
                'email': email,
                'domain': email.split('@')[1] if '@' in email else '',
                'username': email.split('@')[0] if '@' in email else '',
                'scraped_date': datetime.now().strftime("%Y-%m-%d"),
                'scraped_time': datetime.now().strftime("%H:%M:%S")
            })

        saved_files = []

        # Save in JSON format
        if 'json' in formats:
            json_filename = f"{base_filename}_{timestamp}.json"
            json_data = {
                "metadata": {
                    "total_count": len(self.emails),
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "scraping_method": "Advanced Email Scraper"
                },
                "emails": email_data
            }

            with open(json_filename, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, indent=2, ensure_ascii=False)
            saved_files.append(json_filename)
            print(f"✅ Saved {len(self.emails)} emails to {json_filename}")

        # Save in CSV format
        if 'csv' in formats:
            csv_filename = f"{base_filename}_{timestamp}.csv"
            df = pd.DataFrame(email_data)
            df.to_csv(csv_filename, index=False, encoding='utf-8')
            saved_files.append(csv_filename)
            print(f"✅ Saved {len(self.emails)} emails to {csv_filename}")

        # Save in Excel format
        if 'excel' in formats or 'xlsx' in formats:
            excel_filename = f"{base_filename}_{timestamp}.xlsx"
            df = pd.DataFrame(email_data)

            with pd.ExcelWriter(excel_filename, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='Emails', index=False)

                # Add summary sheet
                summary_data = {
                    'Metric': ['Total Emails', 'Unique Domains', 'Scraping Date', 'Scraping Time'],
                    'Value': [
                        len(self.emails),
                        len(set(email.split('@')[1] for email in self.emails if '@' in email)),
                        datetime.now().strftime("%Y-%m-%d"),
                        datetime.now().strftime("%H:%M:%S")
                    ]
                }
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='Summary', index=False)

            saved_files.append(excel_filename)
            print(f"✅ Saved {len(self.emails)} emails to {excel_filename}")

        # Save in TXT format (simple list)
        if 'txt' in formats:
            txt_filename = f"{base_filename}_{timestamp}.txt"
            with open(txt_filename, 'w', encoding='utf-8') as f:
                f.write(f"# Scraped Emails - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"# Total Count: {len(self.emails)}\n\n")
                for email in sorted(self.emails):
                    f.write(f"{email}\n")
            saved_files.append(txt_filename)
            print(f"✅ Saved {len(self.emails)} emails to {txt_filename}")

        # Save in XML format
        if 'xml' in formats:
            xml_filename = f"{base_filename}_{timestamp}.xml"
            with open(xml_filename, 'w', encoding='utf-8') as f:
                f.write('<?xml version="1.0" encoding="UTF-8"?>\n')
                f.write('<emails>\n')
                f.write(f'  <metadata>\n')
                f.write(f'    <total_count>{len(self.emails)}</total_count>\n')
                f.write(f'    <timestamp>{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</timestamp>\n')
                f.write(f'  </metadata>\n')
                f.write(f'  <email_list>\n')
                for i, email in enumerate(email_list, 1):
                    f.write(f'    <email id="{i}">\n')
                    f.write(f'      <address>{email}</address>\n')
                    f.write(f'      <domain>{email.split("@")[1] if "@" in email else ""}</domain>\n')
                    f.write(f'      <username>{email.split("@")[0] if "@" in email else ""}</username>\n')
                    f.write(f'    </email>\n')
                f.write(f'  </email_list>\n')
                f.write('</emails>\n')
            saved_files.append(xml_filename)
            print(f"✅ Saved {len(self.emails)} emails to {xml_filename}")

        # Save in HTML format (table view)
        if 'html' in formats:
            html_filename = f"{base_filename}_{timestamp}.html"
            with open(html_filename, 'w', encoding='utf-8') as f:
                f.write('<!DOCTYPE html>\n<html>\n<head>\n')
                f.write('<title>Scraped Emails</title>\n')
                f.write('<style>\n')
                f.write('table { border-collapse: collapse; width: 100%; }\n')
                f.write('th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n')
                f.write('th { background-color: #f2f2f2; }\n')
                f.write('</style>\n</head>\n<body>\n')
                f.write(f'<h1>Scraped Emails - {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</h1>\n')
                f.write(f'<p>Total Count: {len(self.emails)}</p>\n')
                f.write('<table>\n<tr><th>ID</th><th>Email</th><th>Domain</th><th>Username</th></tr>\n')
                for i, email in enumerate(email_list, 1):
                    domain = email.split('@')[1] if '@' in email else ''
                    username = email.split('@')[0] if '@' in email else ''
                    f.write(f'<tr><td>{i}</td><td>{email}</td><td>{domain}</td><td>{username}</td></tr>\n')
                f.write('</table>\n</body>\n</html>')
            saved_files.append(html_filename)
            print(f"✅ Saved {len(self.emails)} emails to {html_filename}")

        # Generate Google Sheets import instructions
        if 'google-sheets' in formats:
            gs_filename = f"{base_filename}_{timestamp}_google_sheets_import.csv"
            df = pd.DataFrame(email_data)
            df.to_csv(gs_filename, index=False, encoding='utf-8')
            saved_files.append(gs_filename)

            # Create instructions file
            instructions_filename = f"{base_filename}_{timestamp}_google_sheets_instructions.txt"
            with open(instructions_filename, 'w', encoding='utf-8') as f:
                f.write("📊 GOOGLE SHEETS IMPORT INSTRUCTIONS\n")
                f.write("=" * 50 + "\n\n")
                f.write("1. Open Google Sheets (sheets.google.com)\n")
                f.write("2. Create a new spreadsheet\n")
                f.write("3. Go to File > Import\n")
                f.write("4. Choose 'Upload' tab\n")
                f.write(f"5. Upload the file: {gs_filename}\n")
                f.write("6. Select 'Replace spreadsheet' and click 'Import data'\n\n")
                f.write("Alternative method:\n")
                f.write("1. Open the CSV file in Excel\n")
                f.write("2. Copy all data (Ctrl+A, then Ctrl+C)\n")
                f.write("3. Paste into Google Sheets (Ctrl+V)\n\n")
                f.write(f"Total emails to import: {len(self.emails)}\n")

            saved_files.append(instructions_filename)
            print(f"✅ Saved {len(self.emails)} emails for Google Sheets import: {gs_filename}")
            print(f"📋 Import instructions saved: {instructions_filename}")

        print(f"\n📁 Total files saved: {len(saved_files)}")
        return saved_files
        
    def run_scraper(self, sources: List[str], save_formats=['json']):
        """Main scraper execution with configurable save formats"""
        try:
            self.setup_driver()
            print("Starting email scraping...")

            scraped_emails = self.scrape_multiple_sources(sources)
            self.emails.update(scraped_emails)

            print(f"Total emails found: {len(self.emails)}")

            # Save in multiple formats
            if self.emails:
                saved_files = self.save_emails_multiple_formats(formats=save_formats)
                print(f"📁 Emails saved in {len(save_formats)} format(s): {', '.join(save_formats)}")
            else:
                print("⚠️ No emails found to save")

        finally:
            if self.driver:
                self.driver.quit()