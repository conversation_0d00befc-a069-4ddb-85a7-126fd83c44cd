# 🔒 VPN Setup Guide for <PERSON>ail Scraper

This guide explains how to set up VPN and IP rotation capabilities for high-volume email scraping to avoid rate limiting and geographic restrictions.

## 🎯 VPN Options Available

### 1. **Proxy Servers (Recommended for Beginners)**
- **Type**: `vpn_type = "proxy"`
- **Setup**: Automatic - uses free proxy servers
- **Pros**: Easy setup, no additional software needed
- **Cons**: Less reliable, may be slower
- **Cost**: Free

### 2. **Tor Network**
- **Type**: `vpn_type = "tor"`
- **Setup**: Requires Tor installation
- **Pros**: High anonymity, free
- **Cons**: Slower speeds, requires setup
- **Cost**: Free

### 3. **Commercial VPN Services**
- **Type**: `vpn_type = "commercial"`
- **Setup**: Requires VPN subscription and CLI tools
- **Pros**: Fast, reliable, many locations
- **Cons**: Requires paid subscription
- **Cost**: $3-15/month

## 🚀 Quick Start Configuration

### Basic Proxy Setup (Easiest)
```python
# In main.py, set these values:
use_vpn = True
vpn_type = "proxy"
rotation_interval = 25  # Rotate IP every 25 requests
```

### Advanced Configuration
```python
# For high-volume scraping:
use_vpn = True
vpn_type = "proxy"
rotation_interval = 10  # More frequent rotation
max_workers = 50       # More parallel requests
```

## 📋 Detailed Setup Instructions

### Option 1: Proxy Servers (Automatic)
1. Set `vpn_type = "proxy"` in main.py
2. Run the scraper - it will automatically:
   - Find working proxy servers
   - Test their reliability
   - Rotate between them automatically

### Option 2: Tor Network Setup
1. **Install Tor Browser**: Download from https://www.torproject.org/
2. **Or install Tor service**:
   - Windows: Download Tor Expert Bundle
   - Linux: `sudo apt-get install tor`
   - Mac: `brew install tor`
3. **Configure**:
   ```python
   vpn_type = "tor"
   rotation_interval = 20
   ```
4. **Start Tor**: The scraper will attempt to start Tor automatically

### Option 3: Commercial VPN Setup

#### NordVPN Setup
1. **Subscribe**: Get NordVPN subscription
2. **Install CLI**: Download NordVPN CLI tools
3. **Login**: `nordvpn login`
4. **Configure**:
   ```python
   vpn_type = "commercial"
   vpn_provider = "nordvpn"
   rotation_interval = 50
   ```

#### ExpressVPN Setup
1. **Subscribe**: Get ExpressVPN subscription
2. **Install CLI**: Download ExpressVPN CLI
3. **Activate**: Follow activation instructions
4. **Configure**:
   ```python
   vpn_type = "commercial"
   vpn_provider = "expressvpn"
   rotation_interval = 50
   ```

#### SurfShark Setup
1. **Subscribe**: Get SurfShark subscription
2. **Install CLI**: Download SurfShark CLI
3. **Login**: Follow authentication steps
4. **Configure**:
   ```python
   vpn_type = "commercial"
   vpn_provider = "surfshark"
   rotation_interval = 50
   ```

## ⚙️ Configuration Parameters

### VPN Settings
- `use_vpn`: Enable/disable VPN functionality
- `vpn_type`: "proxy", "tor", or "commercial"
- `vpn_provider`: "nordvpn", "expressvpn", "surfshark"
- `rotation_interval`: How often to change IP (lower = more frequent)

### Recommended Settings by Use Case

#### Light Scraping (< 100 emails)
```python
use_vpn = False
max_workers = 5
```

#### Medium Scraping (100-500 emails)
```python
use_vpn = True
vpn_type = "proxy"
rotation_interval = 50
max_workers = 10
```

#### Heavy Scraping (500+ emails)
```python
use_vpn = True
vpn_type = "proxy"
rotation_interval = 25
max_workers = 20
```

#### Maximum Performance (1000+ emails)
```python
use_vpn = True
vpn_type = "commercial"  # or "tor"
rotation_interval = 10
max_workers = 50
```

## 🔍 Monitoring and Troubleshooting

### Check Current IP
The scraper automatically displays:
- Current IP address
- Location information
- Proxy status
- Rotation events

### Common Issues

#### Proxy Connection Failures
- **Solution**: Scraper automatically tries backup proxies
- **Fallback**: Continues without proxy if all fail

#### Tor Connection Issues
- **Check**: Ensure Tor is installed and running
- **Fallback**: Automatically switches to proxy mode

#### Commercial VPN Issues
- **Check**: Verify CLI tools are installed
- **Check**: Ensure you're logged in to VPN service
- **Fallback**: Switches to proxy mode if VPN fails

## 🛡️ Security and Legal Considerations

### Best Practices
1. **Respect Rate Limits**: Don't set rotation_interval too low
2. **Use Reasonable Delays**: Keep smart_delay settings moderate
3. **Monitor Performance**: Watch for blocked requests
4. **Rotate Frequently**: For high-volume scraping

### Legal Compliance
- Ensure scraping complies with website terms of service
- Respect robots.txt files
- Don't overload servers
- Use scraped data responsibly

### Privacy
- VPN/Proxy usage enhances privacy
- Tor provides highest anonymity
- Commercial VPNs offer good balance of speed and privacy

## 📊 Performance Optimization

### For Maximum Speed
```python
use_vpn = True
vpn_type = "proxy"
rotation_interval = 15
max_workers = 30
headless_mode = True
enable_captcha_detection = False
```

### For Maximum Reliability
```python
use_vpn = True
vpn_type = "commercial"
rotation_interval = 100
max_workers = 10
headless_mode = False
enable_captcha_detection = True
```

### For Maximum Anonymity
```python
use_vpn = True
vpn_type = "tor"
rotation_interval = 20
max_workers = 5
headless_mode = True
```

## 🎯 Expected Results

With VPN enabled, you should expect:
- **Higher success rates** due to IP rotation
- **Reduced blocking** from anti-bot measures
- **Geographic diversity** in data sources
- **Scalable scraping** for large datasets
- **Better anonymity** and privacy protection

## 🆘 Support

If you encounter issues:
1. Check the console output for error messages
2. Verify your internet connection
3. Try different VPN types
4. Reduce rotation_interval if getting errors
5. Check VPN service status if using commercial VPN

The scraper includes automatic fallback mechanisms to ensure continued operation even if VPN services fail.
