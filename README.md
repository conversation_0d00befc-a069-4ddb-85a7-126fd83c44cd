# Advanced Email Scraper

A sophisticated Python-based email scraper that uses advanced anti-detection techniques to extract email addresses from websites and Google search results while avoiding modern blocking mechanisms. Includes specialized scrapers for platforms like ZoomInfo.

## Features

### 🚀 Advanced Scraping Capabilities
- **Google Search Integration**: Scrape emails from Google search results
- **Website Crawling**: Direct website email extraction with depth control
- **ZoomInfo Scraper**: Specialized scraper for ZoomInfo people search
- **Multiple Source Support**: Handle both direct URLs and search queries
- **Intelligent Email Detection**: Advanced regex patterns for email extraction
- **Contact Information Extraction**: Names, titles, companies, and emails

### 🛡️ Anti-Detection Technology
- **Undetected Chrome Driver**: Bypasses most bot detection systems
- **Human-like Behavior**: Simulates natural scrolling and mouse movements
- **Random User Agents**: Rotates browser fingerprints automatically
- **Dynamic Delays**: Random timing to avoid pattern detection
- **Popup Handling**: Automatically handles cookie consents and overlays
- **Enhanced Stealth Mode**: Advanced fingerprint masking

### 📊 Smart Data Management
- **Duplicate Prevention**: Automatic email deduplication
- **JSON Export**: Structured data output with timestamps
- **Contact Cards**: Detailed contact information extraction
- **Progress Tracking**: Real-time scraping progress updates
- **Error Handling**: Robust error recovery and logging

## Installation

### Prerequisites
- Python 3.11+
- Google Chrome browser
- UV package manager (recommended)

### Setup
```bash
# Clone the repository
git clone <your-repo-url>
cd email-scraper

# Install dependencies using UV
uv sync

# Alternative: Install with pip
pip install -r requirements.txt
```

## Quick Start

### Basic Usage
```bash
# Run the default scraper
uv run main.py

# Run ZoomInfo example scraper
uv run run_zoominfo_example.py
```

### Custom Configuration
```python
from email_scraper import AdvancedEmailScraper

# Initialize scraper
scraper = AdvancedEmailScraper(headless=False)

# Define sources to scrape
sources = [
    "contact email site:company.com",           # Google search
    "https://example-website.com/contact",     # Direct website
    "business directory emails",               # Search query
]

# Run scraper
scraper.run_scraper(sources)
```

### ZoomInfo Scraping
```python
from zoominfo_scraper import ZoomInfoScraper

# Initialize ZoomInfo scraper
scraper = ZoomInfoScraper(headless=False)

# Target URL
url = "https://www.zoominfo.com/people-search/location-usa-industry-software-title-engineer"

# Run scraper (use responsibly)
scraper.run_scraper(url, max_pages=2)
```

## Configuration Options

### Scraper Settings
```python
# Headless mode (no browser window)
scraper = AdvancedEmailScraper(headless=True)

# ZoomInfo specific settings
zoominfo_scraper = ZoomInfoScraper(headless=True)

# Custom delays and limits
SCRAPING_CONFIG = {
    "delays": {
        "min_page_delay": 2,
        "max_page_delay": 8,
        "between_sources": (10, 30)
    },
    "limits": {
        "max_pages_per_search": 20,
        "max_crawl_depth": 3,
        "max_emails_per_session": 10000
    }
}
```

### Anti-Detection Features
- **Random viewport sizes**: Mimics different screen resolutions
- **User agent rotation**: Changes browser fingerprint per session
- **Human scrolling patterns**: Natural page navigation
- **Mouse movement simulation**: Realistic cursor behavior
- **Cookie consent handling**: Automatic popup dismissal
- **WebDriver property masking**: Removes automation traces

## Output Format

### General Email Results (`scraped_emails.json`)
```json
{
  "emails": [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>"
  ],
  "total_count": 3,
  "timestamp": "2024-01-15 14:30:22"
}
```

### ZoomInfo Results (`zoominfo_results.json`)
```json
{
  "contacts": [
    {
      "name": "John Doe",
      "title": "Software Engineer",
      "company": "Tech Corp",
      "email": "<EMAIL>"
    }
  ],
  "emails": ["<EMAIL>"],
  "total_contacts": 1,
  "total_emails": 1,
  "timestamp": "2024-01-15 14:30:22"
}
```

## Advanced Usage

### Multiple Source Types
```python
sources = [
    # Google search queries
    "contact email site:linkedin.com",
    "business email directory",
    
    # Direct websites
    "https://company.com/contact",
    "https://directory.example.com",
    
    # Specific searches
    "email address filetype:pdf",
    "mailto: site:github.com"
]
```

### Custom Email Patterns
```python
# Add custom email validation
scraper.email_pattern = re.compile(
    r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
)
```

### ZoomInfo Advanced Usage
```python
# Conservative scraping for ZoomInfo
scraper = ZoomInfoScraper(headless=True)

# Custom URL with filters
url = "https://www.zoominfo.com/people-search/location-usa-industry-technology-title-cto"

# Run with minimal pages to avoid detection
scraper.run_scraper(url, max_pages=1)
```

## Best Practices

### ⚖️ Legal and Ethical Guidelines
- **Respect robots.txt**: Always check website scraping policies
- **Rate limiting**: Use appropriate delays between requests
- **Terms of service**: Comply with website terms and conditions
- **Data privacy**: Handle scraped data responsibly
- **Commercial use**: Ensure compliance with applicable laws
- **ZoomInfo specific**: Consider using their official API for commercial purposes

### 🔧 Performance Optimization
- **Batch processing**: Process sources in manageable chunks
- **Memory management**: Monitor Chrome memory usage
- **Error recovery**: Implement retry mechanisms for failed requests
- **Logging**: Enable detailed logging for debugging
- **Conservative pagination**: Limit pages per session

### 🛡️ Security Considerations
- **Proxy support**: Consider using rotating proxies for large-scale scraping
- **VPN usage**: Protect your IP address when scraping
- **Data encryption**: Secure stored email data
- **Access control**: Limit access to scraped data
- **Residential proxies**: Use for high-protection sites like ZoomInfo

## Platform-Specific Notes

### ZoomInfo Scraping
⚠️ **Important Warnings:**
- ZoomInfo has sophisticated anti-bot protection
- Requires careful rate limiting and human-like behavior
- Consider using their official API for commercial use
- Always respect their terms of service
- Use residential proxies for better success rates

**ZoomInfo Features:**
- Contact card extraction (name, title, company)
- Specialized popup handling
- Enhanced anti-detection measures
- Conservative pagination limits

## Troubleshooting

### Common Issues

**Chrome Driver Problems**
```bash
# Update Chrome driver
uv add undetected-chromedriver --upgrade
```

**Memory Issues**
```python
# Reduce concurrent operations
scraper = AdvancedEmailScraper(headless=True)  # Use headless mode
```

**Rate Limiting / Bot Detection**
```python
# Increase delays significantly
time.sleep(random.uniform(10, 20))  # Between requests

# Use residential proxies
options.add_argument('--proxy-server=http://proxy:port')
```

**ZoomInfo Specific Issues**
- **Captcha challenges**: Reduce frequency, use residential proxies
- **Access denied**: Implement longer delays between requests
- **Empty results**: Check if login is required for specific searches

**Captcha Challenges**
- Use residential proxies
- Implement manual captcha solving
- Reduce scraping frequency
- Consider headless mode with proxy rotation

### Error Codes
- `WebDriverException`: Chrome driver compatibility issue
- `TimeoutException`: Page load timeout (increase wait times)
- `NoSuchElementException`: Element not found (update selectors)
- `403 Forbidden`: Rate limited or blocked (increase delays)

## Dependencies

```
selenium>=4.15.2          # Web automation framework
undetected-chromedriver   # Anti-detection Chrome driver
beautifulsoup4>=4.12.2    # HTML parsing
requests>=2.31.0          # HTTP requests
fake-useragent>=1.4.0     # User agent rotation
lxml>=4.9.3              # XML/HTML processing
```

## File Structure

```
email-scraper/
├── main.py                    # Main scraper entry point
├── email_scraper.py          # General email scraper
├── zoominfo_scraper.py       # ZoomInfo specialized scraper
├── run_zoominfo_example.py   # ZoomInfo example runner
├── pyproject.toml            # Project dependencies
├── README.md                 # This file
└── scraped_emails.json       # Output file (generated)
└── zoominfo_results.json     # ZoomInfo output (generated)
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Disclaimer

This tool is for educational and research purposes only. Users are responsible for ensuring their use complies with applicable laws, website terms of service, and ethical guidelines. The developers are not responsible for any misuse of this software.

**Special Note on ZoomInfo**: ZoomInfo has strict terms of service and sophisticated anti-bot protection. Always consider using their official API for commercial purposes and respect their rate limits and terms of service.

## Support

For issues, questions, or contributions:
- Open an issue on GitHub
- Check the troubleshooting section
- Review existing discussions

---

**⚠️ Important**: Always respect website terms of service and applicable laws when scraping. Use responsibly and ethically. For platforms like ZoomInfo, consider official APIs for commercial use.
