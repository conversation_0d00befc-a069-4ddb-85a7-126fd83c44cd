#!/usr/bin/env python3
"""
Ultra-Fast Email Scraper - Designed to scrape 1000+ emails in under 10 minutes
Uses multiple strategies: HTTP scraping, API calls, and targeted website scraping
"""

import asyncio
import aiohttp
import time
import random
import re
import json
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from typing import Set, List
from urllib.parse import quote_plus
import requests
from fake_useragent import UserAgent

class UltraFastEmailScraper:
    def __init__(self, max_workers=50):
        self.emails = set()
        self.max_workers = max_workers
        self.email_pattern = re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')
        self.session = requests.Session()
        self.ua = UserAgent()
        self.session.headers.update({
            'User-Agent': self.ua.random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        })
        
    def is_valid_email(self, email):
        """Validate email and filter false positives"""
        if not email or len(email) < 5:
            return False
        
        invalid_patterns = [
            'example.com', 'test.com', 'domain.com', 'email.com',
            'yourname@', 'name@', 'user@', 'admin@localhost',
            '.png', '.jpg', '.gif', '.css', '.js', 'noreply@',
            'no-reply@', 'donotreply@', 'support@example'
        ]
        
        email_lower = email.lower()
        for pattern in invalid_patterns:
            if pattern in email_lower:
                return False
                
        return True
    
    def extract_emails_from_text(self, text):
        """Extract valid emails from text"""
        emails = set()
        matches = self.email_pattern.findall(text)
        for email in matches:
            if self.is_valid_email(email):
                emails.add(email.lower())
        return emails
    
    def fast_http_scrape(self, url, timeout=3):
        """Ultra-fast HTTP scraping"""
        try:
            response = self.session.get(url, timeout=timeout)
            if response.status_code == 200:
                return self.extract_emails_from_text(response.text)
        except:
            pass
        return set()
    
    def generate_massive_search_urls(self, profession, city, country):
        """Generate hundreds of search URLs for maximum coverage"""
        base_queries = [
            f"{profession} {city} {country}",
            f"hire {profession} {city}",
            f"{profession} consultant {city}",
            f"freelance {profession} {city}",
            f"senior {profession} {city}",
            f"lead {profession} {city}",
            f"{profession} developer {city}",
            f"{profession} engineer {city}",
            f"{profession} specialist {city}",
            f"{profession} expert {city}",
            f"{profession} manager {city}",
            f"{profession} director {city}",
            f"{profession} team {city}",
            f"{profession} company {city}",
            f"{profession} startup {city}",
            f"{profession} agency {city}",
            f"{profession} firm {city}",
            f"{profession} group {city}",
            f"{profession} network {city}",
            f"{profession} community {city}"
        ]
        
        search_modifiers = [
            "email", "contact", "email address", "contact information",
            "email directory", "contact details", "professional email",
            "business email", "work email", "corporate email"
        ]
        
        search_engines = [
            "https://www.google.com/search?q={}&num=100",
            "https://www.bing.com/search?q={}&count=50",
            "https://duckduckgo.com/?q={}",
            "https://search.yahoo.com/search?p={}",
            "https://www.startpage.com/sp/search?query={}",
            "https://searx.org/?q={}"
        ]
        
        urls = []
        for base_query in base_queries:
            for modifier in search_modifiers:
                query = f"{base_query} {modifier}"
                encoded_query = quote_plus(query)
                for engine in search_engines:
                    urls.append(engine.format(encoded_query))
        
        return urls
    
    def scrape_professional_sites(self, profession, city):
        """Scrape professional networking and job sites"""
        professional_sites = [
            f"https://www.linkedin.com/search/results/people/?keywords={profession}%20{city}",
            f"https://www.indeed.com/jobs?q={profession}&l={city}",
            f"https://www.glassdoor.com/Job/jobs.htm?sc.keyword={profession}&locT=C&locId={city}",
            f"https://www.monster.com/jobs/search/?q={profession}&where={city}",
            f"https://www.dice.com/jobs?q={profession}&location={city}",
            f"https://stackoverflow.com/jobs?q={profession}&l={city}",
            f"https://angel.co/jobs?q={profession}&l={city}",
            f"https://www.upwork.com/freelancers/~{profession}?loc={city}",
            f"https://www.freelancer.com/freelancers/{profession}?location={city}",
            f"https://www.guru.com/freelancers/{profession}?location={city}"
        ]
        
        return professional_sites
    
    def parallel_mega_scrape(self, profession, city, country):
        """Massive parallel scraping operation"""
        print(f"🚀 Starting ULTRA-FAST mega scraping for {profession} in {city}, {country}")
        
        # Generate massive URL list
        search_urls = self.generate_massive_search_urls(profession, city, country)
        professional_urls = self.scrape_professional_sites(profession, city)
        all_urls = search_urls + professional_urls
        
        print(f"📊 Generated {len(all_urls)} URLs for parallel scraping")
        
        all_emails = set()
        
        # Use ThreadPoolExecutor for maximum parallel processing
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = [executor.submit(self.fast_http_scrape, url) for url in all_urls]
            
            completed = 0
            for future in as_completed(futures):
                try:
                    result_emails = future.result()
                    all_emails.update(result_emails)
                    completed += 1
                    
                    if completed % 50 == 0:
                        print(f"⚡ Processed {completed}/{len(futures)} URLs, found {len(all_emails)} emails")
                        
                except Exception:
                    completed += 1
                    continue
        
        print(f"✅ Mega scraping completed! Found {len(all_emails)} unique emails")
        return all_emails
    
    def save_emails(self, emails, filename="ultra_fast_emails.json"):
        """Save emails to file"""
        email_data = {
            "emails": list(emails),
            "total_count": len(emails),
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "scraping_method": "Ultra-Fast Parallel Scraping"
        }
        
        with open(filename, 'w') as f:
            json.dump(email_data, f, indent=2)
        
        print(f"💾 Saved {len(emails)} emails to {filename}")
    
    def run_ultra_fast_scrape(self, profession, city, country):
        """Main ultra-fast scraping method"""
        start_time = time.time()
        
        print("🔥 ULTRA-FAST EMAIL SCRAPER STARTING...")
        print(f"Target: {profession} professionals in {city}, {country}")
        print(f"Workers: {self.max_workers}")
        
        # Run mega scraping
        emails = self.parallel_mega_scrape(profession, city, country)
        
        # Save results
        self.save_emails(emails)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n🎯 SCRAPING COMPLETED!")
        print(f"📧 Total emails found: {len(emails)}")
        print(f"⏱️  Time taken: {duration:.2f} seconds ({duration/60:.2f} minutes)")
        print(f"🚀 Speed: {len(emails)/duration:.2f} emails per second")
        
        if len(emails) >= 1000:
            print("🏆 SUCCESS: Achieved 1000+ emails target!")
        else:
            print(f"📈 Found {len(emails)} emails - consider running multiple times or adjusting targets")
        
        return emails

if __name__ == "__main__":
    # Configuration
    profession = "data engineer"
    city = "New York"
    country = "USA"
    max_workers = 100  # Aggressive parallel processing
    
    # Create and run scraper
    scraper = UltraFastEmailScraper(max_workers=max_workers)
    emails = scraper.run_ultra_fast_scrape(profession, city, country)
