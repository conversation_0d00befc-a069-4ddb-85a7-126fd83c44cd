from email_scraper import AdvancedEmailScraper
import sys

def main():
    print("Advanced Email Scraper Starting...")
    
    # Check if URL is provided as command line argument
    if len(sys.argv) > 1:
        target_url = sys.argv[1]
        print(f"Scraping target URL: {target_url}")
        
        # Initialize scraper
        scraper = AdvancedEmailScraper(headless=False)
        
        # Use the provided URL as the only source
        sources =  "https://www.zoominfo.com/people-search/location-usa-industry-software-title-engineer"
    else:
        # Initialize scraper
        scraper = AdvancedEmailScraper(headless=False)
        
        # Define your scraping targets
        sources = [
            "contact email site:company.com",
            "business directory emails",
            "https://example-website.com",
            "email list site:professional-network.com"
        ]
    
    # Run the scraper
    scraper.run_scraper(sources)

if __name__ == "__main__":
    main()


