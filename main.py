from email_scraper import AdvancedEmailScraper

def main():
    print("Advanced Email Scraper Starting...")
    
    # Initialize scraper
    scraper = AdvancedEmailScraper(headless=False)
    
    # Define your scraping targets
    sources = [
        "contact email site:company.com",
        "business directory emails",
        "https://example-website.com",
        "email list site:professional-network.com"
    ]
    
    # Run the scraper
    scraper.run_scraper(sources)

if __name__ == "__main__":
    main()

