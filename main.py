from email_scraper import AdvancedEmailScraper
import sys

def main():
    print("Advanced Email Scraper Starting...")

    # Configuration options for maximum speed (1000+ emails in <10 minutes)
    enable_captcha_detection = False  # Disabled for speed
    headless_mode = True             # Headless mode for faster performance
    max_workers = 20                 # High parallel workers for HTTP requests

    # Target input parameters for focused scraping
    target_profession = "data engineer"  # Change this to your target profession
    target_city = "New York"                 # Change this to your target city
    target_country = "USA"                   # Change this to your target country

    # 📁 SCRAPED EMAILS FILE FORMATS CONFIGURATION
    save_formats = []

    # Enable/disable formats (set to True/False)
    csv_format = True          # 📊 Save as CSV file (Excel compatible)
    json_format = False        # 📄 Save as JSON file (structured data)
    excel_format = True        # 📈 Save as Excel (.xlsx) file with multiple sheets
    txt_format = True          # 📝 Save as simple text file (one email per line)
    xml_format = False         # 🏷️  Save as XML file (structured markup)
    html_format = False        # 🌐 Save as HTML table (web viewable)
    google_sheets = True      # 📊 Generate Google Sheets import files

    # Build formats list based on configuration
    if csv_format:
        save_formats.append('csv')
    if json_format:
        save_formats.append('json')
    if excel_format:
        save_formats.append('excel')
    if txt_format:
        save_formats.append('txt')
    if xml_format:
        save_formats.append('xml')
    if html_format:
        save_formats.append('html')
    if google_sheets:
        save_formats.append('google-sheets')


    # Check if URL is provided as command line argument
    if len(sys.argv) > 1:
        target_url = sys.argv[1]
        print(f"Scraping target URL: {target_url}")

        # Initialize scraper
        scraper = AdvancedEmailScraper(headless=headless_mode, enable_captcha_detection=enable_captcha_detection, max_workers=max_workers)

        # Use the provided URL as the only source
        sources = [target_url]
    else:
        # Initialize scraper
        scraper = AdvancedEmailScraper(headless=headless_mode, enable_captcha_detection=enable_captcha_detection, max_workers=max_workers)

        print(f"Target Profession: {target_profession}")
        print(f"Target City: {target_city}")
        print(f"Target Country: {target_country}")
        print(f"Save Formats: {', '.join(save_formats) if save_formats else 'json (default)'}")

        # Define aggressive scraping targets for 1000+ emails
        sources = [
            # Core profession searches
            f"contact email {target_profession} {target_city} {target_country}",
            f"{target_profession} email directory {target_city}",
            f"business directory {target_profession} {target_city} {target_country}",
            f"professional {target_profession} contact {target_city}",

            # Platform-specific searches
            f"email list {target_profession} site:linkedin.com {target_city}",
            f"{target_profession} site:indeed.com {target_city} contact",
            f"{target_profession} site:glassdoor.com {target_city} email",
            f"{target_profession} site:monster.com {target_city} contact",
            f"{target_profession} site:dice.com {target_city} email",

            # Company and hiring searches
            f"hire {target_profession} {target_city} contact email",
            f"{target_profession} consultant {target_city} email address",
            f"freelance {target_profession} {target_city} contact information",
            f"{target_profession} company {target_city} email directory",
            f"{target_profession} team {target_city} contact details",

            # Seniority-based searches
            f"senior {target_profession} {target_city} email contact",
            f"lead {target_profession} {target_city} contact information",
            f"principal {target_profession} {target_city} email",
            f"head of {target_profession} {target_city} contact",
            f"chief {target_profession} {target_city} email",

            # Industry-specific searches
            f"{target_profession} specialist {target_city} email directory",
            f"{target_profession} expert {target_city} contact",
            f"{target_profession} developer {target_city} email",
            f"{target_profession} analyst {target_city} contact",
            f"{target_profession} manager {target_city} email",

            # Networking and community searches
            f"{target_profession} meetup {target_city} contact",
            f"{target_profession} conference {target_city} email",
            f"{target_profession} community {target_city} contact",
            f"{target_profession} group {target_city} email",
            f"{target_profession} network {target_city} contact"
        ]

    # Run the comprehensive scraper with specified save formats
    scraper.run_scraper_comprehensive(
        sources,
        target_profession=target_profession,
        target_city=target_city,
        target_country=target_country,
        save_formats=save_formats if save_formats else ['json']
    )

if __name__ == "__main__":
    main()


