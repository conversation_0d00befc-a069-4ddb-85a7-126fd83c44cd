from email_scraper import AdvancedEmailScraper
from profession_aliases import PROFESSION_ALIASES, get_profession_aliases
import sys

def main():
    print("Advanced Email Scraper Starting...")

    # Configuration options for maximum speed (1000+ emails in <10 minutes)
    enable_captcha_detection = False  # Disabled for speed
    headless_mode = True             # Headless mode for faster performance
    max_workers = 20                 # High parallel workers for HTTP requests
    output_folder = "ScrapedEmails"  # Folder to save all scraped files

    # 🔒 VPN AND IP ROTATION CONFIGURATION
    use_vpn = True                   # Enable VPN/IP rotation for high-volume scraping
    vpn_type = "proxy"               # Options: "proxy", "tor", "commercial"
    vpn_provider = "nordvpn"         # For commercial VPN: "nordvpn", "expressvpn", "surfshark"
    rotation_interval = 25           # Rotate IP every N requests (lower = more frequent rotation)

    # 🎯 TARGET PROFESSION CONFIGURATION (Multiple Aliases for Maximum Coverage)

    # 📋 SELECT YOUR TARGET PROFESSION (Choose from available options)
    # Available professions: "Data Engineer", "Software Engineer", "DevOps Engineer",
    # "Product Manager", "Data Scientist", "Marketing Manager"
    target_profession_main = "Data Engineer"  # Change this to your target profession

    # 🚀 GET PROFESSION ALIASES WITH SMART SELECTION FOR OPTIMAL PERFORMANCE
    all_aliases = get_profession_aliases(target_profession_main)

    # 🎯 SMART ALIAS SELECTION (Use most effective aliases for better performance)
    # Select top aliases that are most likely to yield results
    priority_aliases = [
        # Core profession terms (always include)
        target_profession_main.lower(),
        "senior " + target_profession_main.lower(),
        "lead " + target_profession_main.lower(),
        "principal " + target_profession_main.lower(),
    ]

    # Add high-value aliases from the database (first 15 most common)
    high_value_aliases = all_aliases[:15] if len(all_aliases) > 15 else all_aliases

    # 📝 CUSTOM ALIASES: Add specific variations for your search
    custom_aliases = [
        "big data engineer",
        "Data Architect",
        "Data Engineering Consultant",
        "Data Warehouse Engineer",
        "ETL Engineer",
        "Analytics Engineer",
        "Data Platform Engineer"
    ]

    # Combine all selected aliases (remove duplicates)
    target_profession_aliases = list(set(priority_aliases + high_value_aliases + custom_aliases))

    print(f"📊 Using {len(target_profession_aliases)} optimized aliases (from {len(all_aliases)} total available)")

    # 💡 SHOW AVAILABLE PROFESSIONS
    print("🎯 Available Professions in Database:")
    for profession in PROFESSION_ALIASES.keys():
        alias_count = len(PROFESSION_ALIASES[profession])
        print(f"   • {profession} ({alias_count} aliases)")
    print()

    # 🌍 LOCATION PARAMETERS
    target_city = "New York"                 # Change this to your target city
    target_country = "USA"                   # Change this to your target country

    # 📁 SCRAPED EMAILS FILE FORMATS CONFIGURATION
    save_formats = []

    # Enable/disable formats (set to True/False)
    csv_format = True          # 📊 Save as CSV file (Excel compatible)
    json_format = False        # 📄 Save as JSON file (structured data)
    excel_format = True        # 📈 Save as Excel (.xlsx) file with multiple sheets
    txt_format = True          # 📝 Save as simple text file (one email per line)
    xml_format = False         # 🏷️  Save as XML file (structured markup)
    html_format = False        # 🌐 Save as HTML table (web viewable)
    google_sheets = True      # 📊 Generate Google Sheets import files

    # Build formats list based on configuration
    if csv_format:
        save_formats.append('csv')
    if json_format:
        save_formats.append('json')
    if excel_format:
        save_formats.append('excel')
    if txt_format:
        save_formats.append('txt')
    if xml_format:
        save_formats.append('xml')
    if html_format:
        save_formats.append('html')
    if google_sheets:
        save_formats.append('google-sheets')


    # Check if URL is provided as command line argument
    if len(sys.argv) > 1:
        target_url = sys.argv[1]
        print(f"Scraping target URL: {target_url}")

        # Initialize scraper
        scraper = AdvancedEmailScraper(
            headless=headless_mode,
            enable_captcha_detection=enable_captcha_detection,
            max_workers=max_workers,
            output_folder=output_folder,
            use_vpn=use_vpn,
            vpn_type=vpn_type,
            vpn_provider=vpn_provider,
            rotation_interval=rotation_interval
        )

        # Use the provided URL as the only source
        sources = [target_url]
    else:
        # Initialize scraper
        scraper = AdvancedEmailScraper(
            headless=headless_mode,
            enable_captcha_detection=enable_captcha_detection,
            max_workers=max_workers,
            output_folder=output_folder,
            use_vpn=use_vpn,
            vpn_type=vpn_type,
            vpn_provider=vpn_provider,
            rotation_interval=rotation_interval
        )

        print(f"Target Profession: {target_profession_main}")
        print(f"Profession Aliases: {len(target_profession_aliases)} variations")
        print(f"Target City: {target_city}")
        print(f"Target Country: {target_country}")
        print(f"Save Formats: {', '.join(save_formats) if save_formats else 'json (default)'}")
        print(f"Output Folder: {output_folder}")
        print(f"VPN Enabled: {use_vpn} ({'Type: ' + vpn_type if use_vpn else 'Direct connection'})")
        if use_vpn:
            print(f"IP Rotation: Every {rotation_interval} requests")

        # 🚀 GENERATE COMPREHENSIVE SEARCH QUERIES FOR ALL PROFESSION ALIASES
        sources = []

        # Search query templates for maximum coverage
        search_templates = [
            # Core profession searches
            "contact email {profession} {city} {country}",
            "{profession} email directory {city}",
            "business directory {profession} {city} {country}",
            "professional {profession} contact {city}",

            # Platform-specific searches
            "email list {profession} site:linkedin.com {city}",
            "{profession} site:indeed.com {city} contact",
            "{profession} site:glassdoor.com {city} email",
            "{profession} site:monster.com {city} contact",
            "{profession} site:dice.com {city} email",

            # Company and hiring searches
            "hire {profession} {city} contact email",
            "{profession} consultant {city} email address",
            "freelance {profession} {city} contact information",
            "{profession} company {city} email directory",
            "{profession} team {city} contact details",

            # Seniority-based searches
            "senior {profession} {city} email contact",
            "lead {profession} {city} contact information",
            "principal {profession} {city} email",
            "head of {profession} {city} contact",
            "chief {profession} {city} email",

            # Industry-specific searches
            "{profession} specialist {city} email directory",
            "{profession} expert {city} contact",
            "{profession} developer {city} email",
            "{profession} analyst {city} contact",
            "{profession} manager {city} email",

            # Networking and community searches
            "{profession} meetup {city} contact",
            "{profession} conference {city} email",
            "{profession} community {city} contact",
            "{profession} group {city} email",
            "{profession} network {city} contact"
        ]

        # Generate sources for each profession alias
        for profession_alias in target_profession_aliases:
            for template in search_templates:
                query = template.format(
                    profession=profession_alias,
                    city=target_city,
                    country=target_country
                )
                sources.append(query)

        print(f"📊 Generated {len(sources)} search queries from {len(target_profession_aliases)} profession aliases")

    # Run the comprehensive scraper with specified save formats and profession aliases
    scraper.run_scraper_comprehensive(
        sources,
        target_profession=target_profession_main,
        target_city=target_city,
        target_country=target_country,
        target_profession_aliases=target_profession_aliases,
        save_formats=save_formats if save_formats else ['json']
    )

if __name__ == "__main__":
    main()


