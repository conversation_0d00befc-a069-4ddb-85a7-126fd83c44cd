from email_scraper import AdvancedEmailScraper
import sys

def main():
    print("Advanced Email Scraper Starting...")

    # Target parameters for focused scraping
    target_profession = "software engineer"  # Change this to your target profession
    target_city = "New York"                 # Change this to your target city
    target_country = "USA"                   # Change this to your target country

    # Check if URL is provided as command line argument
    if len(sys.argv) > 1:
        target_url = sys.argv[1]
        print(f"Scraping target URL: {target_url}")

        # Initialize scraper
        scraper = AdvancedEmailScraper(headless=False)

        # Use the provided URL as the only source
        sources = [target_url]
    else:
        # Initialize scraper
        scraper = AdvancedEmailScraper(headless=False)

        print(f"Target Profession: {target_profession}")
        print(f"Target City: {target_city}")
        print(f"Target Country: {target_country}")

        # Define your scraping targets with location and profession targeting
        sources = [
            f"contact email {target_profession} {target_city} {target_country}",
            f"{target_profession} email directory {target_city}",
            f"business directory {target_profession} {target_city} {target_country}",
            f"professional {target_profession} contact {target_city}",
            f"email list {target_profession} site:linkedin.com {target_city}",
            f"{target_profession} contact information {target_city} {target_country}"
        ]

    # Run the scraper
    scraper.run_scraper(sources)

if __name__ == "__main__":
    main()


