"""
Test script for ethical scraping capabilities
Demonstrates high-volume data collection with ethical practices
"""

from email_scraper import AdvancedEmailScraper
import time

def test_ethical_vs_aggressive_scraping():
    """Compare ethical vs aggressive scraping approaches"""
    
    print("=" * 80)
    print("🌱 ETHICAL SCRAPING VS AGGRESSIVE SCRAPING COMPARISON")
    print("=" * 80)
    
    # Test data
    test_queries = [
        "data engineer <PERSON> York",
        "software engineer <PERSON>", 
        "product manager Boston",
        "marketing manager Chicago",
        "data scientist Seattle"
    ]
    
    # Ethical Scraping Configuration
    print("\n🌱 Testing ETHICAL SCRAPING approach...")
    ethical_scraper = AdvancedEmailScraper(
        headless=True,
        enable_captcha_detection=False,
        max_workers=5,  # Conservative worker count
        output_folder="ScrapedEmails",
        use_vpn=True,
        vpn_type="proxy",
        rotation_interval=50,  # Less frequent rotation
        ethical_mode=True  # Enable ethical practices
    )
    
    # Configure ethical settings
    ethical_scraper.respect_robots_txt = True
    ethical_scraper.max_requests_per_domain = 50
    ethical_scraper.ethical_delay_range = (2, 4)
    
    start_time = time.time()
    ethical_emails = ethical_scraper.parallel_fast_scrape(test_queries)
    ethical_time = time.time() - start_time
    
    print(f"✅ Ethical scraping results:")
    print(f"   📧 Emails found: {len(ethical_emails)}")
    print(f"   ⏱️ Time taken: {ethical_time:.1f} seconds")
    print(f"   🌐 Domains accessed: {len(ethical_scraper.domain_request_count)}")
    print(f"   🚫 Domains blocked: {len(ethical_scraper.blocked_domains)}")
    print(f"   📊 Avg requests/domain: {sum(ethical_scraper.domain_request_count.values()) / max(len(ethical_scraper.domain_request_count), 1):.1f}")
    
    # Aggressive Scraping Configuration (for comparison)
    print("\n⚡ Testing AGGRESSIVE SCRAPING approach...")
    aggressive_scraper = AdvancedEmailScraper(
        headless=True,
        enable_captcha_detection=False,
        max_workers=20,  # High worker count
        output_folder="ScrapedEmails",
        use_vpn=True,
        vpn_type="proxy", 
        rotation_interval=10,  # Frequent rotation
        ethical_mode=False  # Disable ethical practices
    )
    
    start_time = time.time()
    aggressive_emails = aggressive_scraper.parallel_fast_scrape(test_queries)
    aggressive_time = time.time() - start_time
    
    print(f"✅ Aggressive scraping results:")
    print(f"   📧 Emails found: {len(aggressive_emails)}")
    print(f"   ⏱️ Time taken: {aggressive_time:.1f} seconds")
    print(f"   🌐 Domains accessed: {len(aggressive_scraper.domain_request_count)}")
    print(f"   🚫 Domains blocked: {len(aggressive_scraper.blocked_domains)}")
    
    # Comparison
    print("\n📊 COMPARISON RESULTS:")
    print(f"   Speed difference: {aggressive_time/ethical_time:.1f}x faster (aggressive)")
    print(f"   Email difference: {len(aggressive_emails) - len(ethical_emails)} more emails (aggressive)")
    print(f"   Blocking rate: Ethical: {len(ethical_scraper.blocked_domains)}, Aggressive: {len(aggressive_scraper.blocked_domains)}")
    print(f"   Sustainability: Ethical approach is more sustainable long-term")
    
    return ethical_emails, aggressive_emails

def test_high_volume_ethical_scraping():
    """Test high-volume ethical scraping with profession aliases"""
    
    print("\n" + "=" * 80)
    print("🎯 HIGH-VOLUME ETHICAL SCRAPING TEST")
    print("=" * 80)
    
    # Initialize scraper with optimal ethical settings
    scraper = AdvancedEmailScraper(
        headless=True,
        enable_captcha_detection=False,
        max_workers=10,
        output_folder="ScrapedEmails",
        use_vpn=True,
        vpn_type="proxy",
        rotation_interval=25,
        ethical_mode=True
    )
    
    # Configure for high-volume ethical scraping
    scraper.respect_robots_txt = True
    scraper.max_requests_per_domain = 100
    scraper.ethical_delay_range = (1, 3)  # Faster but still ethical
    
    # Test with multiple professions and cities
    professions = ["Data Engineer", "Software Engineer", "Product Manager"]
    cities = ["New York", "San Francisco", "Boston", "Seattle", "Chicago"]
    
    # Generate comprehensive search queries
    sources = []
    for profession in professions:
        for city in cities:
            sources.extend([
                f"{profession} {city}",
                f"Senior {profession} {city}",
                f"Lead {profession} {city}"
            ])
    
    print(f"🚀 Testing with {len(sources)} search queries...")
    print(f"🌱 Ethical mode enabled with:")
    print(f"   - Robots.txt compliance: {scraper.respect_robots_txt}")
    print(f"   - Max requests per domain: {scraper.max_requests_per_domain}")
    print(f"   - Delay range: {scraper.ethical_delay_range} seconds")
    print(f"   - IP rotation every: {scraper.rotation_interval} requests")
    
    start_time = time.time()
    
    # Run comprehensive scraping
    contacts = scraper.scrape_multiple_sources_comprehensive(
        sources=sources,
        target_profession="Data Engineer",
        target_city="New York", 
        target_country="USA"
    )
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # Results
    print(f"\n✅ HIGH-VOLUME ETHICAL SCRAPING COMPLETED!")
    print(f"   📧 Total contacts found: {len(contacts)}")
    print(f"   ⏱️ Total time: {total_time/60:.1f} minutes")
    print(f"   🌐 Domains accessed: {len(scraper.domain_request_count)}")
    print(f"   🚫 Domains blocked: {len(scraper.blocked_domains)}")
    print(f"   📊 Requests per minute: {scraper.request_count/(total_time/60):.1f}")
    print(f"   🎯 Success rate: {(len(contacts)/len(sources))*100:.1f}%")
    
    # Save results
    if contacts:
        saved_files = scraper.save_emails_multiple_formats(
            base_filename="ethical_scraping_test",
            formats=['csv', 'excel', 'json', 'txt']
        )
        print(f"   💾 Results saved to: {saved_files}")
    
    # Ethical compliance report
    print(f"\n🌱 ETHICAL COMPLIANCE REPORT:")
    print(f"   ✅ Robots.txt checked for {len(scraper.robots_txt_cache)} domains")
    print(f"   ✅ Rate limiting applied to all requests")
    print(f"   ✅ Respectful delays implemented")
    print(f"   ✅ Domain request limits enforced")
    print(f"   ✅ Automatic blocking for problematic domains")
    
    return contacts

def test_different_ethical_levels():
    """Test different levels of ethical scraping"""
    
    print("\n" + "=" * 80)
    print("⚖️ DIFFERENT ETHICAL LEVELS COMPARISON")
    print("=" * 80)
    
    test_queries = ["data engineer New York", "software engineer Boston"]
    
    # Level 1: Maximum Ethics (Slowest, Most Respectful)
    print("\n🌱 Level 1: MAXIMUM ETHICS")
    max_ethical = AdvancedEmailScraper(
        max_workers=3,
        ethical_mode=True,
        use_vpn=False
    )
    max_ethical.max_requests_per_domain = 25
    max_ethical.ethical_delay_range = (5, 10)
    
    start = time.time()
    emails_max = max_ethical.parallel_fast_scrape(test_queries)
    time_max = time.time() - start
    
    print(f"   📧 Emails: {len(emails_max)}, ⏱️ Time: {time_max:.1f}s")
    
    # Level 2: Balanced Ethics (Medium Speed, Good Practices)
    print("\n⚖️ Level 2: BALANCED ETHICS")
    balanced_ethical = AdvancedEmailScraper(
        max_workers=8,
        ethical_mode=True,
        use_vpn=True,
        vpn_type="proxy"
    )
    balanced_ethical.max_requests_per_domain = 75
    balanced_ethical.ethical_delay_range = (2, 4)
    
    start = time.time()
    emails_balanced = balanced_ethical.parallel_fast_scrape(test_queries)
    time_balanced = time.time() - start
    
    print(f"   📧 Emails: {len(emails_balanced)}, ⏱️ Time: {time_balanced:.1f}s")
    
    # Level 3: Minimal Ethics (Faster, Basic Compliance)
    print("\n⚡ Level 3: MINIMAL ETHICS")
    min_ethical = AdvancedEmailScraper(
        max_workers=15,
        ethical_mode=True,
        use_vpn=True,
        vpn_type="proxy"
    )
    min_ethical.max_requests_per_domain = 150
    min_ethical.ethical_delay_range = (0.5, 2)
    
    start = time.time()
    emails_min = min_ethical.parallel_fast_scrape(test_queries)
    time_min = time.time() - start
    
    print(f"   📧 Emails: {len(emails_min)}, ⏱️ Time: {time_min:.1f}s")
    
    # Summary
    print(f"\n📊 ETHICAL LEVELS SUMMARY:")
    print(f"   Maximum Ethics: {len(emails_max)} emails in {time_max:.1f}s")
    print(f"   Balanced Ethics: {len(emails_balanced)} emails in {time_balanced:.1f}s") 
    print(f"   Minimal Ethics: {len(emails_min)} emails in {time_min:.1f}s")
    print(f"   💡 Recommendation: Use Balanced Ethics for optimal results")

if __name__ == "__main__":
    print("🧪 ETHICAL SCRAPING TEST SUITE")
    print("Testing various ethical scraping configurations...")
    
    # Run tests
    try:
        # Test 1: Compare ethical vs aggressive
        test_ethical_vs_aggressive_scraping()
        
        # Test 2: High-volume ethical scraping
        test_high_volume_ethical_scraping()
        
        # Test 3: Different ethical levels
        test_different_ethical_levels()
        
        print("\n" + "=" * 80)
        print("✅ ALL ETHICAL SCRAPING TESTS COMPLETED SUCCESSFULLY!")
        print("🌱 Ethical scraping enables sustainable high-volume data collection")
        print("📊 Results demonstrate the balance between speed and responsibility")
        print("=" * 80)
        
    except KeyboardInterrupt:
        print("\n⚠️ Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Test error: {e}")
        print("💡 Check your configuration and try again")
