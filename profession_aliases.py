"""
Profession Aliases Configuration
Comprehensive list of profession variations and aliases for maximum scraping coverage
"""

# 🎯 PROFESSION ALIASES DATABASE
# Add all possible variations, titles, and aliases for each profession

PROFESSION_ALIASES = {
    "Data Engineer": [
        "data engineer",
        "big data engineer", 
        "data architect",
        "databricks engineer",
        "data platform engineer",
        "data infrastructure engineer",
        "senior data engineer",
        "lead data engineer",
        "principal data engineer",
        "staff data engineer",
        "data engineering manager",
        "data engineering lead",
        "data pipeline engineer",
        "etl engineer",
        "data warehouse engineer",
        "analytics engineer",
        "data scientist engineer",
        "machine learning engineer",
        "ml engineer",
        "data ops engineer",
        "data devops engineer",
        "dataops engineer",
        "cloud data engineer",
        "aws data engineer",
        "azure data engineer",
        "gcp data engineer",
        "google cloud data engineer",
        "spark engineer",
        "apache spark engineer",
        "kafka engineer",
        "hadoop engineer",
        "snowflake engineer",
        "airflow engineer",
        "apache airflow engineer",
        "dbt engineer",
        "data build tool engineer",
        "streaming data engineer",
        "real-time data engineer",
        "batch data engineer",
        "data integration engineer",
        "data migration engineer"
    ],
    
    "Software Engineer": [
        "software engineer",
        "software developer",
        "full stack engineer",
        "full stack developer",
        "backend engineer",
        "backend developer",
        "frontend engineer",
        "frontend developer",
        "web developer",
        "application developer",
        "systems engineer",
        "senior software engineer",
        "lead software engineer",
        "principal software engineer",
        "staff software engineer",
        "software engineering manager",
        "development engineer",
        "programmer",
        "coding engineer",
        "java developer",
        "python developer",
        "javascript developer",
        "react developer",
        "node.js developer",
        "angular developer",
        "vue.js developer",
        ".net developer",
        "c# developer",
        "php developer",
        "ruby developer",
        "go developer",
        "rust developer",
        "scala developer",
        "kotlin developer",
        "swift developer",
        "mobile developer",
        "ios developer",
        "android developer",
        "react native developer",
        "flutter developer"
    ],
    
    "DevOps Engineer": [
        "devops engineer",
        "site reliability engineer",
        "sre",
        "platform engineer",
        "infrastructure engineer",
        "cloud engineer",
        "systems administrator",
        "build engineer",
        "release engineer",
        "deployment engineer",
        "automation engineer",
        "ci/cd engineer",
        "kubernetes engineer",
        "docker engineer",
        "container engineer",
        "aws engineer",
        "azure engineer",
        "gcp engineer",
        "terraform engineer",
        "ansible engineer",
        "jenkins engineer",
        "gitlab engineer",
        "github actions engineer",
        "monitoring engineer",
        "observability engineer",
        "security engineer",
        "cloud security engineer",
        "network engineer",
        "linux administrator",
        "unix administrator",
        "windows administrator"
    ],
    
    "Product Manager": [
        "product manager",
        "senior product manager",
        "lead product manager",
        "principal product manager",
        "staff product manager",
        "product management",
        "product owner",
        "technical product manager",
        "digital product manager",
        "software product manager",
        "platform product manager",
        "data product manager",
        "ai product manager",
        "mobile product manager",
        "web product manager",
        "product strategy manager",
        "product marketing manager",
        "associate product manager",
        "junior product manager",
        "product analyst",
        "product coordinator",
        "product specialist",
        "product lead",
        "head of product",
        "vp of product",
        "chief product officer",
        "cpo"
    ],
    
    "Data Scientist": [
        "data scientist",
        "senior data scientist",
        "lead data scientist",
        "principal data scientist",
        "staff data scientist",
        "data science manager",
        "machine learning scientist",
        "ml scientist",
        "research scientist",
        "applied scientist",
        "quantitative analyst",
        "data analyst",
        "business analyst",
        "analytics engineer",
        "statistician",
        "data researcher",
        "ai scientist",
        "artificial intelligence scientist",
        "deep learning scientist",
        "computer vision scientist",
        "nlp scientist",
        "natural language processing scientist",
        "predictive analyst",
        "modeling scientist",
        "algorithm engineer",
        "data mining specialist",
        "business intelligence analyst",
        "bi analyst",
        "reporting analyst",
        "insights analyst"
    ],
    
    "Marketing Manager": [
        "marketing manager",
        "digital marketing manager",
        "content marketing manager",
        "social media manager",
        "email marketing manager",
        "performance marketing manager",
        "growth marketing manager",
        "brand manager",
        "product marketing manager",
        "marketing director",
        "marketing lead",
        "marketing specialist",
        "marketing coordinator",
        "marketing analyst",
        "campaign manager",
        "advertising manager",
        "seo manager",
        "sem manager",
        "ppc manager",
        "marketing automation manager",
        "demand generation manager",
        "field marketing manager",
        "event marketing manager",
        "partnership marketing manager",
        "affiliate marketing manager",
        "influencer marketing manager",
        "community manager",
        "public relations manager",
        "communications manager",
        "marketing operations manager"
    ]
}

def get_profession_aliases(profession_name):
    """Get all aliases for a given profession"""
    return PROFESSION_ALIASES.get(profession_name, [profession_name.lower()])

def get_all_professions():
    """Get list of all available professions"""
    return list(PROFESSION_ALIASES.keys())

def add_custom_aliases(profession_name, aliases):
    """Add custom aliases for a profession"""
    if profession_name not in PROFESSION_ALIASES:
        PROFESSION_ALIASES[profession_name] = []
    PROFESSION_ALIASES[profession_name].extend(aliases)

# 🎯 QUICK PROFESSION SELECTOR
# Uncomment and modify the profession you want to target

SELECTED_PROFESSION = "Data Engineer"  # Change this to target different profession

# Get aliases for selected profession
CURRENT_ALIASES = get_profession_aliases(SELECTED_PROFESSION)
